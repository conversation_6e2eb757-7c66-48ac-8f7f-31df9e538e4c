import request from '@/utils/request'

// 查询反馈分类列表
export function listCategory(query) {
  return request({
    url: '/feedback/category/list',
    method: 'get',
    params: query
  })
}

// 查询启用的反馈分类列表
export function listEnabledCategory() {
  return request({
    url: '/feedback/category/enabled',
    method: 'get'
  })
}

// 查询反馈分类详细
export function getCategory(id) {
  return request({
    url: '/feedback/category/' + id,
    method: 'get'
  })
}

// 根据分类编码查询反馈分类
export function getCategoryByCode(code) {
  return request({
    url: '/feedback/category/code/' + code,
    method: 'get'
  })
}

// 新增反馈分类
export function addCategory(data) {
  return request({
    url: '/feedback/category',
    method: 'post',
    data: data
  })
}

// 修改反馈分类
export function updateCategory(data) {
  return request({
    url: '/feedback/category',
    method: 'put',
    data: data
  })
}

// 删除反馈分类
export function delCategory(id) {
  return request({
    url: '/feedback/category/' + id,
    method: 'delete'
  })
}

// 检查分类编码是否唯一
export function checkCodeUnique(data) {
  return request({
    url: '/feedback/category/checkCodeUnique',
    method: 'post',
    data: data
  })
}
