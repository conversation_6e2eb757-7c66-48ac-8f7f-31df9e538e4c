<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="建议标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入建议标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建议类型" prop="suggestionType">
        <el-select v-model="queryParams.suggestionType" placeholder="请选择建议类型" clearable>
          <el-option label="内容优化" value="content" />
          <el-option label="结构调整" value="structure" />
          <el-option label="标签完善" value="tag" />
          <el-option label="检索优化" value="search" />
        </el-select>
      </el-form-item>
      <el-form-item label="目标类型" prop="targetType">
        <el-select v-model="queryParams.targetType" placeholder="请选择目标类型" clearable>
          <el-option label="知识库" value="knowledge_base" />
          <el-option label="文档" value="document" />
          <el-option label="分类" value="category" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="已实施" value="3" />
          <el-option label="已关闭" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="自动生成" prop="autoGenerated">
        <el-select v-model="queryParams.autoGenerated" placeholder="请选择" clearable>
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['feedback:suggestion:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['feedback:suggestion:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleReview"
          v-hasPermi="['feedback:suggestion:review']"
        >审核</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Setting"
          :disabled="multiple"
          @click="handleImplement"
          v-hasPermi="['feedback:suggestion:implement']"
        >实施</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['feedback:suggestion:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['feedback:suggestion:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="suggestionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="建议ID" align="center" prop="id" width="80" />
      <el-table-column label="建议标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="建议类型" align="center" prop="suggestionType" width="100">
        <template #default="scope">
          <dict-tag :options="suggestion_type" :value="scope.row.suggestionType"/>
        </template>
      </el-table-column>
      <el-table-column label="目标类型" align="center" prop="targetType" width="100">
        <template #default="scope">
          <dict-tag :options="target_type" :value="scope.row.targetType"/>
        </template>
      </el-table-column>
      <el-table-column label="目标名称" align="center" prop="targetName" :show-overflow-tooltip="true" />
      <el-table-column label="优先级" align="center" prop="priority" width="80">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)">{{ getPriorityText(scope.row.priority) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="置信度" align="center" prop="confidenceScore" width="100">
        <template #default="scope">
          <el-progress :percentage="Math.round(scope.row.confidenceScore * 100)" :stroke-width="6" v-if="scope.row.confidenceScore"/>
        </template>
      </el-table-column>
      <el-table-column label="自动生成" align="center" prop="autoGenerated" width="100">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.autoGenerated"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="suggestion_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="reviewerName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['feedback:suggestion:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['feedback:suggestion:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['feedback:suggestion:review']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="review" icon="Check">审核</el-dropdown-item>
                <el-dropdown-item command="implement" icon="Setting">实施</el-dropdown-item>
                <el-dropdown-item command="evaluate" icon="Star">评估</el-dropdown-item>
                <el-dropdown-item command="close" icon="Close">关闭</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库优化建议对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="suggestionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="建议标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入建议标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建议类型" prop="suggestionType">
              <el-select v-model="form.suggestionType" placeholder="请选择建议类型">
                <el-option label="内容优化" value="content" />
                <el-option label="结构调整" value="structure" />
                <el-option label="标签完善" value="tag" />
                <el-option label="检索优化" value="search" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="目标类型" prop="targetType">
              <el-select v-model="form.targetType" placeholder="请选择目标类型">
                <el-option label="知识库" value="knowledge_base" />
                <el-option label="文档" value="document" />
                <el-option label="分类" value="category" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标名称" prop="targetName">
              <el-input v-model="form.targetName" placeholder="请输入目标名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="建议描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入建议描述" />
        </el-form-item>
        <el-form-item label="当前内容" prop="currentContent">
          <el-input v-model="form.currentContent" type="textarea" :rows="4" placeholder="请输入当前内容" />
        </el-form-item>
        <el-form-item label="建议内容" prop="suggestedContent">
          <el-input v-model="form.suggestedContent" type="textarea" :rows="4" placeholder="请输入建议内容" />
        </el-form-item>
        <el-form-item label="优化原因" prop="optimizationReason">
          <el-input v-model="form.optimizationReason" type="textarea" :rows="3" placeholder="请输入优化原因" />
        </el-form-item>
        <el-form-item label="预期效果" prop="expectedEffect">
          <el-input v-model="form.expectedEffect" type="textarea" :rows="3" placeholder="请输入预期效果" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级">
                <el-option label="高" value="1" />
                <el-option label="中" value="2" />
                <el-option label="低" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="置信度" prop="confidenceScore">
              <el-input-number v-model="form.confidenceScore" :precision="2" :step="0.1" :max="1" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 建议详情对话框 -->
    <el-dialog title="建议详情" v-model="detailOpen" width="900px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="建议ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="建议标题">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="建议类型">
          <dict-tag :options="suggestion_type" :value="detailForm.suggestionType"/>
        </el-descriptions-item>
        <el-descriptions-item label="目标类型">
          <dict-tag :options="target_type" :value="detailForm.targetType"/>
        </el-descriptions-item>
        <el-descriptions-item label="目标名称">{{ detailForm.targetName }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(detailForm.priority)">{{ getPriorityText(detailForm.priority) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="置信度">
          <el-progress :percentage="Math.round(detailForm.confidenceScore * 100)" :stroke-width="6" v-if="detailForm.confidenceScore"/>
        </el-descriptions-item>
        <el-descriptions-item label="自动生成">
          <dict-tag :options="sys_yes_no" :value="detailForm.autoGenerated"/>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="suggestion_status" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="审核人">{{ detailForm.reviewerName }}</el-descriptions-item>
        <el-descriptions-item label="实施人">{{ detailForm.implementerName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="建议描述" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.description }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="当前内容" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.currentContent }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="建议内容" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.suggestedContent }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="优化原因" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.optimizationReason }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="预期效果" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.expectedEffect }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2" v-if="detailForm.reviewComment">
          <div style="white-space: pre-wrap;">{{ detailForm.reviewComment }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="实施结果" :span="2" v-if="detailForm.implementResult">
          <div style="white-space: pre-wrap;">{{ detailForm.implementResult }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="效果评估" :span="2" v-if="detailForm.effectEvaluation">
          <div style="white-space: pre-wrap;">{{ detailForm.effectEvaluation }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeOptimizationSuggestion">
import { listSuggestion, getSuggestion, delSuggestion, addSuggestion, updateSuggestion, reviewSuggestion, implementSuggestion, evaluateSuggestion, closeSuggestion } from "@/api/feedback/suggestion";

const { proxy } = getCurrentInstance();
const { suggestion_status, suggestion_type, target_type, sys_yes_no } = proxy.useDict('suggestion_status', 'suggestion_type', 'target_type', 'sys_yes_no');

const suggestionList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    suggestionType: null,
    targetType: null,
    status: null,
    autoGenerated: null
  },
  rules: {
    title: [
      { required: true, message: "建议标题不能为空", trigger: "blur" }
    ],
    suggestionType: [
      { required: true, message: "建议类型不能为空", trigger: "change" }
    ],
    targetType: [
      { required: true, message: "目标类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询知识库优化建议列表 */
function getList() {
  loading.value = true;
  listSuggestion(queryParams.value).then(response => {
    suggestionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 优先级类型
function getPriorityType(priority) {
  switch (priority) {
    case '1': return 'danger';
    case '2': return 'warning';
    case '3': return 'info';
    default: return '';
  }
}

// 优先级文本
function getPriorityText(priority) {
  switch (priority) {
    case '1': return '高';
    case '2': return '中';
    case '3': return '低';
    default: return '';
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    description: null,
    suggestionType: null,
    targetType: null,
    targetName: null,
    currentContent: null,
    suggestedContent: null,
    optimizationReason: null,
    expectedEffect: null,
    priority: "2",
    confidenceScore: null,
    remark: null
  };
  proxy.resetForm("suggestionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加知识库优化建议";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getSuggestion(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改知识库优化建议";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  getSuggestion(row.id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["suggestionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSuggestion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSuggestion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除知识库优化建议编号为"' + _ids + '"的数据项？').then(function() {
    return delSuggestion(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('feedback/suggestion/export', {
    ...queryParams.value
  }, `suggestion_${new Date().getTime()}.xlsx`)
}

/** 审核按钮操作 */
function handleReview(row) {
  proxy.$modal.msgInfo("审核功能待实现");
}

/** 实施按钮操作 */
function handleImplement(row) {
  proxy.$modal.msgInfo("实施功能待实现");
}

/** 下拉菜单命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'review':
      handleReview(row);
      break;
    case 'implement':
      handleImplement(row);
      break;
    case 'evaluate':
      proxy.$modal.msgInfo("评估功能待实现");
      break;
    case 'close':
      proxy.$modal.msgInfo("关闭功能待实现");
      break;
  }
}

onMounted(() => {
  getList();
});
</script>
