<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.feedback.mapper.FeedbackProcessLogMapper">
    
    <resultMap type="FeedbackProcessLog" id="FeedbackProcessLogResult">
        <result property="id"    column="id"    />
        <result property="feedbackId"    column="feedback_id"    />
        <result property="actionType"    column="action_type"    />
        <result property="actionDescription"    column="action_description"    />
        <result property="oldStatus"    column="old_status"    />
        <result property="newStatus"    column="new_status"    />
        <result property="oldHandlerId"    column="old_handler_id"    />
        <result property="newHandlerId"    column="new_handler_id"    />
        <result property="processContent"    column="process_content"    />
        <result property="attachments"    column="attachments"    />
        <result property="timeSpent"    column="time_spent"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFeedbackProcessLogVo">
        select id, feedback_id, action_type, action_description, old_status, new_status, old_handler_id, new_handler_id, process_content, attachments, time_spent, operator_id, operator_name, create_time, remark from feedback_process_log
    </sql>

    <select id="selectFeedbackProcessLogList" parameterType="FeedbackProcessLog" resultMap="FeedbackProcessLogResult">
        <include refid="selectFeedbackProcessLogVo"/>
        <where>  
            <if test="feedbackId != null "> and feedback_id = #{feedbackId}</if>
            <if test="actionType != null  and actionType != ''"> and action_type = #{actionType}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectFeedbackProcessLogById" parameterType="Long" resultMap="FeedbackProcessLogResult">
        <include refid="selectFeedbackProcessLogVo"/>
        where id = #{id}
    </select>

    <select id="selectFeedbackProcessLogByFeedbackId" parameterType="Long" resultMap="FeedbackProcessLogResult">
        <include refid="selectFeedbackProcessLogVo"/>
        where feedback_id = #{feedbackId}
        order by create_time desc
    </select>

    <select id="selectFeedbackProcessLogByActionType" parameterType="String" resultMap="FeedbackProcessLogResult">
        <include refid="selectFeedbackProcessLogVo"/>
        where action_type = #{actionType}
        order by create_time desc
    </select>

    <select id="selectFeedbackProcessLogByOperatorId" parameterType="Long" resultMap="FeedbackProcessLogResult">
        <include refid="selectFeedbackProcessLogVo"/>
        where operator_id = #{operatorId}
        order by create_time desc
    </select>
        
    <insert id="insertFeedbackProcessLog" parameterType="FeedbackProcessLog" useGeneratedKeys="true" keyProperty="id">
        insert into feedback_process_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedbackId != null">feedback_id,</if>
            <if test="actionType != null and actionType != ''">action_type,</if>
            <if test="actionDescription != null">action_description,</if>
            <if test="oldStatus != null">old_status,</if>
            <if test="newStatus != null">new_status,</if>
            <if test="oldHandlerId != null">old_handler_id,</if>
            <if test="newHandlerId != null">new_handler_id,</if>
            <if test="processContent != null">process_content,</if>
            <if test="attachments != null">attachments,</if>
            <if test="timeSpent != null">time_spent,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedbackId != null">#{feedbackId},</if>
            <if test="actionType != null and actionType != ''">#{actionType},</if>
            <if test="actionDescription != null">#{actionDescription},</if>
            <if test="oldStatus != null">#{oldStatus},</if>
            <if test="newStatus != null">#{newStatus},</if>
            <if test="oldHandlerId != null">#{oldHandlerId},</if>
            <if test="newHandlerId != null">#{newHandlerId},</if>
            <if test="processContent != null">#{processContent},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="timeSpent != null">#{timeSpent},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateFeedbackProcessLog" parameterType="FeedbackProcessLog">
        update feedback_process_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="actionDescription != null">action_description = #{actionDescription},</if>
            <if test="processContent != null">process_content = #{processContent},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="timeSpent != null">time_spent = #{timeSpent},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeedbackProcessLogById" parameterType="Long">
        delete from feedback_process_log where id = #{id}
    </delete>

    <delete id="deleteFeedbackProcessLogByIds" parameterType="String">
        delete from feedback_process_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteFeedbackProcessLogByFeedbackId" parameterType="Long">
        delete from feedback_process_log where feedback_id = #{feedbackId}
    </delete>

</mapper>
