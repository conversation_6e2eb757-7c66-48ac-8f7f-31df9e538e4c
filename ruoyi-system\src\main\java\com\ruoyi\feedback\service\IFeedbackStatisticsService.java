package com.ruoyi.feedback.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import com.ruoyi.feedback.domain.FeedbackStatistics;

/**
 * 反馈统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IFeedbackStatisticsService 
{
    /**
     * 查询反馈统计
     * 
     * @param id 反馈统计主键
     * @return 反馈统计
     */
    public FeedbackStatistics selectFeedbackStatisticsById(Long id);

    /**
     * 查询反馈统计列表
     * 
     * @param feedbackStatistics 反馈统计
     * @return 反馈统计集合
     */
    public List<FeedbackStatistics> selectFeedbackStatisticsList(FeedbackStatistics feedbackStatistics);

    /**
     * 根据统计日期和类型查询统计数据
     * 
     * @param statDate 统计日期
     * @param statType 统计类型
     * @param categoryId 分类ID（可为空）
     * @return 反馈统计
     */
    public FeedbackStatistics selectFeedbackStatisticsByDateAndType(Date statDate, String statType, Long categoryId);

    /**
     * 根据日期范围查询统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @return 反馈统计集合
     */
    public List<FeedbackStatistics> selectFeedbackStatisticsByDateRange(Date startDate, Date endDate, String statType);

    /**
     * 新增反馈统计
     * 
     * @param feedbackStatistics 反馈统计
     * @return 结果
     */
    public int insertFeedbackStatistics(FeedbackStatistics feedbackStatistics);

    /**
     * 修改反馈统计
     * 
     * @param feedbackStatistics 反馈统计
     * @return 结果
     */
    public int updateFeedbackStatistics(FeedbackStatistics feedbackStatistics);

    /**
     * 批量删除反馈统计
     * 
     * @param ids 需要删除的反馈统计主键集合
     * @return 结果
     */
    public int deleteFeedbackStatisticsByIds(Long[] ids);

    /**
     * 删除反馈统计信息
     * 
     * @param id 反馈统计主键
     * @return 结果
     */
    public int deleteFeedbackStatisticsById(Long id);

    /**
     * 生成日统计数据
     * 
     * @param statDate 统计日期
     * @return 结果
     */
    public int generateDailyStatistics(Date statDate);

    /**
     * 生成周统计数据
     * 
     * @param statDate 统计日期（周的开始日期）
     * @return 结果
     */
    public int generateWeeklyStatistics(Date statDate);

    /**
     * 生成月统计数据
     * 
     * @param statDate 统计日期（月的开始日期）
     * @return 结果
     */
    public int generateMonthlyStatistics(Date statDate);

    /**
     * 自动生成统计数据（定时任务调用）
     * 
     * @return 结果
     */
    public int autoGenerateStatistics();

    /**
     * 获取反馈趋势数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @return 趋势数据
     */
    public List<Map<String, Object>> getFeedbackTrendData(Date startDate, Date endDate, String statType);

    /**
     * 获取反馈分类分布数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分布数据
     */
    public List<Map<String, Object>> getFeedbackCategoryDistribution(Date startDate, Date endDate);

    /**
     * 获取反馈状态分布数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分布数据
     */
    public List<Map<String, Object>> getFeedbackStatusDistribution(Date startDate, Date endDate);

    /**
     * 获取反馈处理效率数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 效率数据
     */
    public Map<String, Object> getFeedbackProcessingEfficiency(Date startDate, Date endDate);

    /**
     * 获取反馈满意度数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 满意度数据
     */
    public Map<String, Object> getFeedbackSatisfactionData(Date startDate, Date endDate);

    /**
     * 获取反馈概览数据（仪表板用）
     * 
     * @return 概览数据
     */
    public Map<String, Object> getFeedbackOverviewData();

    /**
     * 清理过期统计数据
     * 
     * @param beforeDate 清理此日期之前的数据
     * @return 结果
     */
    public int cleanExpiredStatistics(Date beforeDate);
}
