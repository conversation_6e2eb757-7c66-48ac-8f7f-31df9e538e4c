<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.feedback.mapper.UserFeedbackMapper">
    
    <resultMap type="UserFeedback" id="UserFeedbackResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="rating"    column="rating"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="knowledgeBaseId"    column="knowledge_base_id"    />
        <result property="knowledgeBaseName"    column="knowledge_base_name"    />
        <result property="documentId"    column="document_id"    />
        <result property="documentName"    column="document_name"    />
        <result property="aiSessionId"    column="ai_session_id"    />
        <result property="sourceType"    column="source_type"    />
        <result property="sourcePage"    column="source_page"    />
        <result property="status"    column="status"    />
        <result property="handlerId"    column="handler_id"    />
        <result property="handlerName"    column="handler_name"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleResult"    column="handle_result"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectUserFeedbackVo">
        select id, title, content, category_id, category_name, rating, user_id, user_name, knowledge_base_id, knowledge_base_name, document_id, document_name, ai_session_id, source_type, source_page, status, handler_id, handler_name, handle_time, handle_result, create_by, create_time, update_by, update_time, remark from user_feedback
    </sql>

    <select id="selectUserFeedbackList" parameterType="UserFeedback" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="rating != null "> and rating = #{rating}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="knowledgeBaseId != null "> and knowledge_base_id = #{knowledgeBaseId}</if>
            <if test="documentId != null "> and document_id = #{documentId}</if>
            <if test="aiSessionId != null  and aiSessionId != ''"> and ai_session_id = #{aiSessionId}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="handlerId != null "> and handler_id = #{handlerId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectUserFeedbackById" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where id = #{id}
    </select>

    <select id="selectUserFeedbackByUserId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByKnowledgeBaseId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where knowledge_base_id = #{knowledgeBaseId}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByDocumentId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where document_id = #{documentId}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByAiSessionId" parameterType="String" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where ai_session_id = #{aiSessionId}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByCategoryId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where category_id = #{categoryId}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByStatus" parameterType="String" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where status = #{status}
        order by create_time desc
    </select>

    <select id="selectUserFeedbackByHandlerId" parameterType="Long" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where handler_id = #{handlerId}
        order by create_time desc
    </select>

    <select id="selectPendingUserFeedbackList" resultMap="UserFeedbackResult">
        <include refid="selectUserFeedbackVo"/>
        where status = '0'
        order by create_time asc
    </select>

    <select id="selectFeedbackCountByCategory" resultType="map">
        select category_id, category_name, count(*) as count
        from user_feedback
        where status != '3'
        group by category_id, category_name
        order by count desc
    </select>

    <select id="selectFeedbackCountByStatus" resultType="map">
        select status, count(*) as count
        from user_feedback
        group by status
        order by status
    </select>
        
    <insert id="insertUserFeedback" parameterType="UserFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into user_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="categoryName != null">category_name,</if>
            <if test="rating != null">rating,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="knowledgeBaseId != null">knowledge_base_id,</if>
            <if test="knowledgeBaseName != null">knowledge_base_name,</if>
            <if test="documentId != null">document_id,</if>
            <if test="documentName != null">document_name,</if>
            <if test="aiSessionId != null">ai_session_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourcePage != null">source_page,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="categoryName != null">#{categoryName},</if>
            <if test="rating != null">#{rating},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="knowledgeBaseId != null">#{knowledgeBaseId},</if>
            <if test="knowledgeBaseName != null">#{knowledgeBaseName},</if>
            <if test="documentId != null">#{documentId},</if>
            <if test="documentName != null">#{documentName},</if>
            <if test="aiSessionId != null">#{aiSessionId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourcePage != null">#{sourcePage},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateUserFeedback" parameterType="UserFeedback">
        update user_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handlerId != null">handler_id = #{handlerId},</if>
            <if test="handlerName != null">handler_name = #{handlerName},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleResult != null">handle_result = #{handleResult},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateFeedbackStatus">
        update user_feedback set 
        status = #{status},
        <if test="handlerId != null">handler_id = #{handlerId},</if>
        <if test="handlerName != null">handler_name = #{handlerName},</if>
        handle_time = sysdate(),
        update_time = sysdate()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteUserFeedbackById" parameterType="Long">
        delete from user_feedback where id = #{id}
    </delete>

    <delete id="deleteUserFeedbackByIds" parameterType="String">
        delete from user_feedback where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
