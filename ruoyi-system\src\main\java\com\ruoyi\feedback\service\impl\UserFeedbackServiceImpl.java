package com.ruoyi.feedback.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.feedback.mapper.UserFeedbackMapper;
import com.ruoyi.feedback.domain.UserFeedback;
import com.ruoyi.feedback.service.IUserFeedbackService;
import com.ruoyi.feedback.service.IFeedbackProcessLogService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 用户反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Service
public class UserFeedbackServiceImpl implements IUserFeedbackService 
{
    @Autowired
    private UserFeedbackMapper userFeedbackMapper;

    @Autowired
    private IFeedbackProcessLogService feedbackProcessLogService;

    /**
     * 查询用户反馈
     * 
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    @Override
    public UserFeedback selectUserFeedbackById(Long id)
    {
        return userFeedbackMapper.selectUserFeedbackById(id);
    }

    /**
     * 查询用户反馈列表
     * 
     * @param userFeedback 用户反馈
     * @return 用户反馈
     */
    @Override
    public List<UserFeedback> selectUserFeedbackList(UserFeedback userFeedback)
    {
        return userFeedbackMapper.selectUserFeedbackList(userFeedback);
    }

    /**
     * 根据用户ID查询反馈列表
     * 
     * @param userId 用户ID
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectUserFeedbackByUserId(Long userId)
    {
        return userFeedbackMapper.selectUserFeedbackByUserId(userId);
    }

    /**
     * 根据知识库ID查询反馈列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectUserFeedbackByKnowledgeBaseId(Long knowledgeBaseId)
    {
        return userFeedbackMapper.selectUserFeedbackByKnowledgeBaseId(knowledgeBaseId);
    }

    /**
     * 根据文档ID查询反馈列表
     * 
     * @param documentId 文档ID
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectUserFeedbackByDocumentId(Long documentId)
    {
        return userFeedbackMapper.selectUserFeedbackByDocumentId(documentId);
    }

    /**
     * 根据AI会话ID查询反馈列表
     * 
     * @param aiSessionId AI会话ID
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectUserFeedbackByAiSessionId(String aiSessionId)
    {
        return userFeedbackMapper.selectUserFeedbackByAiSessionId(aiSessionId);
    }

    /**
     * 查询待处理的反馈列表
     * 
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectPendingUserFeedbackList()
    {
        return userFeedbackMapper.selectPendingUserFeedbackList();
    }

    /**
     * 统计反馈数量按分类
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectFeedbackCountByCategory()
    {
        return userFeedbackMapper.selectFeedbackCountByCategory();
    }

    /**
     * 统计反馈数量按状态
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectFeedbackCountByStatus()
    {
        return userFeedbackMapper.selectFeedbackCountByStatus();
    }

    /**
     * 新增用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int insertUserFeedback(UserFeedback userFeedback)
    {
        userFeedback.setCreateTime(DateUtils.getNowDate());
        return userFeedbackMapper.insertUserFeedback(userFeedback);
    }

    /**
     * 修改用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int updateUserFeedback(UserFeedback userFeedback)
    {
        userFeedback.setUpdateTime(DateUtils.getNowDate());
        return userFeedbackMapper.updateUserFeedback(userFeedback);
    }

    /**
     * 分配反馈给处理人
     * 
     * @param feedbackId 反馈ID
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int assignFeedback(Long feedbackId, Long handlerId, String handlerName)
    {
        UserFeedback feedback = userFeedbackMapper.selectUserFeedbackById(feedbackId);
        if (feedback == null) {
            return 0;
        }

        Long oldHandlerId = feedback.getHandlerId();
        
        // 更新反馈信息
        UserFeedback updateFeedback = new UserFeedback();
        updateFeedback.setId(feedbackId);
        updateFeedback.setHandlerId(handlerId);
        updateFeedback.setHandlerName(handlerName);
        updateFeedback.setStatus("1"); // 设置为处理中
        updateFeedback.setUpdateTime(DateUtils.getNowDate());
        
        int result = userFeedbackMapper.updateUserFeedback(updateFeedback);
        
        // 记录处理日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackAssign(feedbackId, oldHandlerId, handlerId, 
                SecurityUtils.getUserId(), SecurityUtils.getUsername());
        }
        
        return result;
    }

    /**
     * 批量分配反馈给处理人
     * 
     * @param feedbackIds 反馈ID数组
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int batchAssignFeedback(Long[] feedbackIds, Long handlerId, String handlerName)
    {
        int result = userFeedbackMapper.batchUpdateFeedbackStatus(feedbackIds, "1", handlerId, handlerName);
        
        // 记录处理日志
        if (result > 0) {
            for (Long feedbackId : feedbackIds) {
                feedbackProcessLogService.logFeedbackAssign(feedbackId, null, handlerId, 
                    SecurityUtils.getUserId(), SecurityUtils.getUsername());
            }
        }
        
        return result;
    }

    /**
     * 处理反馈
     * 
     * @param feedbackId 反馈ID
     * @param handleResult 处理结果
     * @return 结果
     */
    @Override
    @Transactional
    public int processFeedback(Long feedbackId, String handleResult)
    {
        UserFeedback updateFeedback = new UserFeedback();
        updateFeedback.setId(feedbackId);
        updateFeedback.setStatus("2"); // 设置为已处理
        updateFeedback.setHandleResult(handleResult);
        updateFeedback.setHandleTime(DateUtils.getNowDate());
        updateFeedback.setUpdateTime(DateUtils.getNowDate());
        
        int result = userFeedbackMapper.updateUserFeedback(updateFeedback);
        
        // 记录处理日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackProcess(feedbackId, handleResult, 
                SecurityUtils.getUserId(), SecurityUtils.getUsername());
        }
        
        return result;
    }

    /**
     * 关闭反馈
     * 
     * @param feedbackId 反馈ID
     * @param closeReason 关闭原因
     * @return 结果
     */
    @Override
    @Transactional
    public int closeFeedback(Long feedbackId, String closeReason)
    {
        UserFeedback updateFeedback = new UserFeedback();
        updateFeedback.setId(feedbackId);
        updateFeedback.setStatus("3"); // 设置为已关闭
        updateFeedback.setHandleResult(closeReason);
        updateFeedback.setHandleTime(DateUtils.getNowDate());
        updateFeedback.setUpdateTime(DateUtils.getNowDate());
        
        int result = userFeedbackMapper.updateUserFeedback(updateFeedback);
        
        // 记录处理日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackClose(feedbackId, closeReason, 
                SecurityUtils.getUserId(), SecurityUtils.getUsername());
        }
        
        return result;
    }

    /**
     * 重新打开反馈
     * 
     * @param feedbackId 反馈ID
     * @param reopenReason 重开原因
     * @return 结果
     */
    @Override
    @Transactional
    public int reopenFeedback(Long feedbackId, String reopenReason)
    {
        UserFeedback updateFeedback = new UserFeedback();
        updateFeedback.setId(feedbackId);
        updateFeedback.setStatus("0"); // 设置为待处理
        updateFeedback.setUpdateTime(DateUtils.getNowDate());
        
        int result = userFeedbackMapper.updateUserFeedback(updateFeedback);
        
        // 记录处理日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackReopen(feedbackId, reopenReason, 
                SecurityUtils.getUserId(), SecurityUtils.getUsername());
        }
        
        return result;
    }

    /**
     * 批量更新反馈状态
     * 
     * @param feedbackIds 反馈ID数组
     * @param status 新状态
     * @return 结果
     */
    @Override
    public int batchUpdateFeedbackStatus(Long[] feedbackIds, String status)
    {
        return userFeedbackMapper.batchUpdateFeedbackStatus(feedbackIds, status, null, null);
    }

    /**
     * 批量删除用户反馈
     * 
     * @param ids 需要删除的用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteUserFeedbackByIds(Long[] ids)
    {
        return userFeedbackMapper.deleteUserFeedbackByIds(ids);
    }

    /**
     * 删除用户反馈信息
     * 
     * @param id 用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteUserFeedbackById(Long id)
    {
        return userFeedbackMapper.deleteUserFeedbackById(id);
    }

    /**
     * 提交用户反馈（包含业务逻辑处理）
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    @Transactional
    public int submitUserFeedback(UserFeedback userFeedback)
    {
        // 设置默认值
        if (StringUtils.isEmpty(userFeedback.getSourceType())) {
            userFeedback.setSourceType("manual");
        }
        if (StringUtils.isEmpty(userFeedback.getStatus())) {
            userFeedback.setStatus("0"); // 待处理
        }
        
        userFeedback.setCreateTime(DateUtils.getNowDate());
        int result = userFeedbackMapper.insertUserFeedback(userFeedback);
        
        // 记录创建日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackCreate(userFeedback.getId(), 
                userFeedback.getUserId(), userFeedback.getUserName());
        }
        
        return result;
    }

    /**
     * 自动收集反馈（系统自动生成）
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    @Transactional
    public int autoCollectFeedback(UserFeedback userFeedback)
    {
        userFeedback.setSourceType("auto");
        userFeedback.setStatus("0"); // 待处理
        userFeedback.setCreateTime(DateUtils.getNowDate());
        
        int result = userFeedbackMapper.insertUserFeedback(userFeedback);
        
        // 记录创建日志
        if (result > 0) {
            feedbackProcessLogService.logFeedbackCreate(userFeedback.getId(), 
                null, "系统自动收集");
        }
        
        return result;
    }
}
