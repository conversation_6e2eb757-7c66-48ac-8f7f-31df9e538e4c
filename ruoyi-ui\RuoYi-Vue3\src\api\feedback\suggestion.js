import request from '@/utils/request'

// 查询知识库优化建议列表
export function listSuggestion(query) {
  return request({
    url: '/feedback/suggestion/list',
    method: 'get',
    params: query
  })
}

// 查询自动生成的优化建议列表
export function listAutoGeneratedSuggestion() {
  return request({
    url: '/feedback/suggestion/auto',
    method: 'get'
  })
}

// 查询待审核的优化建议列表
export function listPendingReviewSuggestion() {
  return request({
    url: '/feedback/suggestion/pending',
    method: 'get'
  })
}

// 查询已通过待实施的优化建议列表
export function listApprovedSuggestion() {
  return request({
    url: '/feedback/suggestion/approved',
    method: 'get'
  })
}

// 根据建议类型查询优化建议列表
export function listSuggestionByType(suggestionType) {
  return request({
    url: '/feedback/suggestion/type/' + suggestionType,
    method: 'get'
  })
}

// 根据目标查询优化建议列表
export function listSuggestionByTarget(targetType, targetId) {
  return request({
    url: '/feedback/suggestion/target/' + targetType + '/' + targetId,
    method: 'get'
  })
}

// 获取优化建议统计数据
export function getSuggestionStatistics() {
  return request({
    url: '/feedback/suggestion/statistics',
    method: 'get'
  })
}

// 查询知识库优化建议详细
export function getSuggestion(id) {
  return request({
    url: '/feedback/suggestion/' + id,
    method: 'get'
  })
}

// 新增知识库优化建议
export function addSuggestion(data) {
  return request({
    url: '/feedback/suggestion',
    method: 'post',
    data: data
  })
}

// 修改知识库优化建议
export function updateSuggestion(data) {
  return request({
    url: '/feedback/suggestion',
    method: 'put',
    data: data
  })
}

// 删除知识库优化建议
export function delSuggestion(id) {
  return request({
    url: '/feedback/suggestion/' + id,
    method: 'delete'
  })
}

// 审核优化建议
export function reviewSuggestion(id, status, reviewComment) {
  return request({
    url: '/feedback/suggestion/review/' + id,
    method: 'put',
    params: {
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 批量审核优化建议
export function batchReviewSuggestion(ids, status) {
  return request({
    url: '/feedback/suggestion/batchReview',
    method: 'put',
    params: {
      ids: ids,
      status: status
    }
  })
}

// 实施优化建议
export function implementSuggestion(id, implementResult) {
  return request({
    url: '/feedback/suggestion/implement/' + id,
    method: 'put',
    params: {
      implementResult: implementResult
    }
  })
}

// 关闭优化建议
export function closeSuggestion(id, closeReason) {
  return request({
    url: '/feedback/suggestion/close/' + id,
    method: 'put',
    params: {
      closeReason: closeReason
    }
  })
}

// 评估优化建议效果
export function evaluateSuggestion(id, effectEvaluation) {
  return request({
    url: '/feedback/suggestion/evaluate/' + id,
    method: 'put',
    params: {
      effectEvaluation: effectEvaluation
    }
  })
}

// 基于反馈数据生成优化建议
export function generateSuggestionFromFeedback(feedbackIds) {
  return request({
    url: '/feedback/suggestion/generateFromFeedback',
    method: 'post',
    params: {
      feedbackIds: feedbackIds
    }
  })
}

// 自动生成知识库优化建议
export function autoGenerateOptimizationSuggestions(knowledgeBaseId) {
  return request({
    url: '/feedback/suggestion/autoGenerate',
    method: 'post',
    params: {
      knowledgeBaseId: knowledgeBaseId
    }
  })
}
