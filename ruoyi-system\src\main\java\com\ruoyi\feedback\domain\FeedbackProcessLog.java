package com.ruoyi.feedback.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 反馈处理记录对象 feedback_process_log
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class FeedbackProcessLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 反馈ID */
    @Excel(name = "反馈ID")
    private Long feedbackId;

    /** 操作类型（create创建 assign分配 process处理 close关闭 reopen重开） */
    @Excel(name = "操作类型", readConverterExp = "create=创建,assign=分配,process=处理,close=关闭,reopen=重开")
    private String actionType;

    /** 操作描述 */
    @Excel(name = "操作描述")
    private String actionDescription;

    /** 原状态 */
    @Excel(name = "原状态", readConverterExp = "0=待处理,1=处理中,2=已处理,3=已关闭")
    private String oldStatus;

    /** 新状态 */
    @Excel(name = "新状态", readConverterExp = "0=待处理,1=处理中,2=已处理,3=已关闭")
    private String newStatus;

    /** 原处理人ID */
    @Excel(name = "原处理人ID")
    private Long oldHandlerId;

    /** 新处理人ID */
    @Excel(name = "新处理人ID")
    private Long newHandlerId;

    /** 处理内容 */
    @Excel(name = "处理内容")
    private String processContent;

    /** 附件信息（JSON格式） */
    @Excel(name = "附件信息")
    private String attachments;

    /** 耗时（分钟） */
    @Excel(name = "耗时（分钟）")
    private Integer timeSpent;

    /** 操作人ID */
    @Excel(name = "操作人ID")
    private Long operatorId;

    /** 操作人姓名 */
    @Excel(name = "操作人姓名")
    private String operatorName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setFeedbackId(Long feedbackId) 
    {
        this.feedbackId = feedbackId;
    }

    public Long getFeedbackId() 
    {
        return feedbackId;
    }

    public void setActionType(String actionType) 
    {
        this.actionType = actionType;
    }

    public String getActionType() 
    {
        return actionType;
    }

    public void setActionDescription(String actionDescription) 
    {
        this.actionDescription = actionDescription;
    }

    public String getActionDescription() 
    {
        return actionDescription;
    }

    public void setOldStatus(String oldStatus) 
    {
        this.oldStatus = oldStatus;
    }

    public String getOldStatus() 
    {
        return oldStatus;
    }

    public void setNewStatus(String newStatus) 
    {
        this.newStatus = newStatus;
    }

    public String getNewStatus() 
    {
        return newStatus;
    }

    public void setOldHandlerId(Long oldHandlerId) 
    {
        this.oldHandlerId = oldHandlerId;
    }

    public Long getOldHandlerId() 
    {
        return oldHandlerId;
    }

    public void setNewHandlerId(Long newHandlerId) 
    {
        this.newHandlerId = newHandlerId;
    }

    public Long getNewHandlerId() 
    {
        return newHandlerId;
    }

    public void setProcessContent(String processContent) 
    {
        this.processContent = processContent;
    }

    public String getProcessContent() 
    {
        return processContent;
    }

    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }

    public void setTimeSpent(Integer timeSpent) 
    {
        this.timeSpent = timeSpent;
    }

    public Integer getTimeSpent() 
    {
        return timeSpent;
    }

    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }

    public void setOperatorName(String operatorName) 
    {
        this.operatorName = operatorName;
    }

    public String getOperatorName() 
    {
        return operatorName;
    }

    @Override
    public Date getCreateTime() 
    {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("feedbackId", getFeedbackId())
            .append("actionType", getActionType())
            .append("actionDescription", getActionDescription())
            .append("oldStatus", getOldStatus())
            .append("newStatus", getNewStatus())
            .append("oldHandlerId", getOldHandlerId())
            .append("newHandlerId", getNewHandlerId())
            .append("processContent", getProcessContent())
            .append("attachments", getAttachments())
            .append("timeSpent", getTimeSpent())
            .append("operatorId", getOperatorId())
            .append("operatorName", getOperatorName())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
}
