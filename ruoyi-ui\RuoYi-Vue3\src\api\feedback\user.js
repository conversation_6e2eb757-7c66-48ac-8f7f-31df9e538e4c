import request from '@/utils/request'

// 查询用户反馈列表
export function listFeedback(query) {
  return request({
    url: '/feedback/user/list',
    method: 'get',
    params: query
  })
}

// 查询我的反馈列表
export function listMyFeedback(query) {
  return request({
    url: '/feedback/user/my',
    method: 'get',
    params: query
  })
}

// 查询待处理的反馈列表
export function listPendingFeedback() {
  return request({
    url: '/feedback/user/pending',
    method: 'get'
  })
}

// 查询我处理的反馈列表
export function listHandledFeedback(query) {
  return request({
    url: '/feedback/user/handled',
    method: 'get',
    params: query
  })
}

// 根据知识库ID查询反馈列表
export function listFeedbackByKnowledgeBase(knowledgeBaseId) {
  return request({
    url: '/feedback/user/knowledge/' + knowledgeBaseId,
    method: 'get'
  })
}

// 根据文档ID查询反馈列表
export function listFeedbackByDocument(documentId) {
  return request({
    url: '/feedback/user/document/' + documentId,
    method: 'get'
  })
}

// 根据AI会话ID查询反馈列表
export function listFeedbackByAiSession(aiSessionId) {
  return request({
    url: '/feedback/user/session/' + aiSessionId,
    method: 'get'
  })
}

// 获取反馈统计数据
export function getFeedbackStatistics() {
  return request({
    url: '/feedback/user/statistics',
    method: 'get'
  })
}

// 查询用户反馈详细
export function getFeedback(id) {
  return request({
    url: '/feedback/user/' + id,
    method: 'get'
  })
}

// 新增用户反馈
export function addFeedback(data) {
  return request({
    url: '/feedback/user',
    method: 'post',
    data: data
  })
}

// 修改用户反馈
export function updateFeedback(data) {
  return request({
    url: '/feedback/user',
    method: 'put',
    data: data
  })
}

// 删除用户反馈
export function delFeedback(id) {
  return request({
    url: '/feedback/user/' + id,
    method: 'delete'
  })
}

// 分配反馈给处理人
export function assignFeedback(id, handlerId, handlerName) {
  return request({
    url: '/feedback/user/assign/' + id,
    method: 'put',
    params: {
      handlerId: handlerId,
      handlerName: handlerName
    }
  })
}

// 批量分配反馈给处理人
export function batchAssignFeedback(ids, handlerId, handlerName) {
  return request({
    url: '/feedback/user/batchAssign',
    method: 'put',
    params: {
      ids: ids,
      handlerId: handlerId,
      handlerName: handlerName
    }
  })
}

// 处理反馈
export function processFeedback(id, handleResult) {
  return request({
    url: '/feedback/user/process/' + id,
    method: 'put',
    params: {
      handleResult: handleResult
    }
  })
}

// 关闭反馈
export function closeFeedback(id, closeReason) {
  return request({
    url: '/feedback/user/close/' + id,
    method: 'put',
    params: {
      closeReason: closeReason
    }
  })
}

// 重新打开反馈
export function reopenFeedback(id, reopenReason) {
  return request({
    url: '/feedback/user/reopen/' + id,
    method: 'put',
    params: {
      reopenReason: reopenReason
    }
  })
}

// 批量更新反馈状态
export function batchUpdateFeedbackStatus(ids, status) {
  return request({
    url: '/feedback/user/batchUpdateStatus',
    method: 'put',
    params: {
      ids: ids,
      status: status
    }
  })
}

// 自动收集反馈
export function autoCollectFeedback(data) {
  return request({
    url: '/feedback/user/autoCollect',
    method: 'post',
    data: data
  })
}
