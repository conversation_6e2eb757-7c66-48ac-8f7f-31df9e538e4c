package com.ruoyi.feedback.mapper;

import java.util.List;
import com.ruoyi.feedback.domain.FeedbackProcessLog;

/**
 * 反馈处理记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface FeedbackProcessLogMapper 
{
    /**
     * 查询反馈处理记录
     * 
     * @param id 反馈处理记录主键
     * @return 反馈处理记录
     */
    public FeedbackProcessLog selectFeedbackProcessLogById(Long id);

    /**
     * 查询反馈处理记录列表
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogList(FeedbackProcessLog feedbackProcessLog);

    /**
     * 根据反馈ID查询处理记录列表
     * 
     * @param feedbackId 反馈ID
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByFeedbackId(Long feedbackId);

    /**
     * 根据操作类型查询处理记录列表
     * 
     * @param actionType 操作类型
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByActionType(String actionType);

    /**
     * 根据操作人ID查询处理记录列表
     * 
     * @param operatorId 操作人ID
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByOperatorId(Long operatorId);

    /**
     * 新增反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    public int insertFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog);

    /**
     * 修改反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    public int updateFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog);

    /**
     * 删除反馈处理记录
     * 
     * @param id 反馈处理记录主键
     * @return 结果
     */
    public int deleteFeedbackProcessLogById(Long id);

    /**
     * 批量删除反馈处理记录
     * 
     * @param ids 需要删除的反馈处理记录主键集合
     * @return 结果
     */
    public int deleteFeedbackProcessLogByIds(Long[] ids);

    /**
     * 根据反馈ID删除处理记录
     * 
     * @param feedbackId 反馈ID
     * @return 结果
     */
    public int deleteFeedbackProcessLogByFeedbackId(Long feedbackId);
}
