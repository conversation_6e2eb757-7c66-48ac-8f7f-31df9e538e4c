<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="反馈标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入反馈标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="反馈分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择反馈分类" clearable>
          <el-option
            v-for="category in categoryOptions"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable>
          <el-option label="待处理" value="0" />
          <el-option label="处理中" value="1" />
          <el-option label="已处理" value="2" />
          <el-option label="已关闭" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈来源" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择反馈来源" clearable>
          <el-option label="用户手动提交" value="manual" />
          <el-option label="系统自动收集" value="auto" />
          <el-option label="管理员创建" value="system" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['feedback:user:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['feedback:user:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="User"
          :disabled="multiple"
          @click="handleAssign"
          v-hasPermi="['feedback:user:assign']"
        >分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleProcess"
          v-hasPermi="['feedback:user:handle']"
        >处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['feedback:user:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['feedback:user:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="反馈ID" align="center" prop="id" width="80" />
      <el-table-column label="反馈标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="反馈分类" align="center" prop="categoryName" width="100" />
      <el-table-column label="评分" align="center" prop="rating" width="80">
        <template #default="scope">
          <el-rate v-model="scope.row.rating" disabled show-score text-color="#ff9900" score-template="{value}" v-if="scope.row.rating"/>
        </template>
      </el-table-column>
      <el-table-column label="反馈用户" align="center" prop="userName" width="100" />
      <el-table-column label="知识库" align="center" prop="knowledgeBaseName" :show-overflow-tooltip="true" />
      <el-table-column label="反馈来源" align="center" prop="sourceType" width="100">
        <template #default="scope">
          <dict-tag :options="feedback_source_type" :value="scope.row.sourceType"/>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="feedback_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="handlerName" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['feedback:user:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['feedback:user:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['feedback:user:handle']">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="assign" icon="User">分配</el-dropdown-item>
                <el-dropdown-item command="process" icon="Check">处理</el-dropdown-item>
                <el-dropdown-item command="close" icon="Close">关闭</el-dropdown-item>
                <el-dropdown-item command="reopen" icon="Refresh">重开</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户反馈对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="feedbackRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="反馈标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入反馈标题" />
        </el-form-item>
        <el-form-item label="反馈内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入反馈内容" />
        </el-form-item>
        <el-form-item label="反馈分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择反馈分类" @change="handleCategoryChange">
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评分" prop="rating">
          <el-rate v-model="form.rating" show-text />
        </el-form-item>
        <el-form-item label="知识库" prop="knowledgeBaseId">
          <el-select v-model="form.knowledgeBaseId" placeholder="请选择知识库" clearable filterable>
            <el-option
              v-for="kb in knowledgeBaseOptions"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源页面" prop="sourcePage">
          <el-input v-model="form.sourcePage" placeholder="请输入来源页面" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 反馈详情对话框 -->
    <el-dialog title="反馈详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="反馈ID">{{ detailForm.id }}</el-descriptions-item>
        <el-descriptions-item label="反馈标题">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="反馈分类">{{ detailForm.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="评分">
          <el-rate v-model="detailForm.rating" disabled show-score v-if="detailForm.rating"/>
        </el-descriptions-item>
        <el-descriptions-item label="反馈用户">{{ detailForm.userName }}</el-descriptions-item>
        <el-descriptions-item label="反馈来源">{{ detailForm.sourceType }}</el-descriptions-item>
        <el-descriptions-item label="知识库">{{ detailForm.knowledgeBaseName }}</el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <dict-tag :options="feedback_status" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="处理人">{{ detailForm.handlerName }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ parseTime(detailForm.handleTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="反馈内容" :span="2">
          <div style="white-space: pre-wrap;">{{ detailForm.content }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="处理结果" :span="2" v-if="detailForm.handleResult">
          <div style="white-space: pre-wrap;">{{ detailForm.handleResult }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="detailForm.remark">
          <div style="white-space: pre-wrap;">{{ detailForm.remark }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="UserFeedback">
import { listFeedback, getFeedback, delFeedback, addFeedback, updateFeedback, assignFeedback, processFeedback, closeFeedback, reopenFeedback } from "@/api/feedback/user";
import { listEnabledCategory } from "@/api/feedback/category";

const { proxy } = getCurrentInstance();
const { feedback_status, feedback_source_type } = proxy.useDict('feedback_status', 'feedback_source_type');

const feedbackList = ref([]);
const categoryOptions = ref([]);
const knowledgeBaseOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    categoryId: null,
    status: null,
    sourceType: null
  },
  rules: {
    title: [
      { required: true, message: "反馈标题不能为空", trigger: "blur" }
    ],
    content: [
      { required: true, message: "反馈内容不能为空", trigger: "blur" }
    ],
    categoryId: [
      { required: true, message: "反馈分类不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询用户反馈列表 */
function getList() {
  loading.value = true;
  listFeedback(queryParams.value).then(response => {
    feedbackList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询反馈分类选项 */
function getCategoryOptions() {
  listEnabledCategory().then(response => {
    categoryOptions.value = response.data;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    content: null,
    categoryId: null,
    categoryName: null,
    rating: null,
    knowledgeBaseId: null,
    sourcePage: null,
    remark: null
  };
  proxy.resetForm("feedbackRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户反馈";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getFeedback(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改用户反馈";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  getFeedback(row.id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 分类变更处理 */
function handleCategoryChange(categoryId) {
  const category = categoryOptions.value.find(item => item.id === categoryId);
  if (category) {
    form.value.categoryName = category.name;
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["feedbackRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateFeedback(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addFeedback(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除用户反馈编号为"' + _ids + '"的数据项？').then(function() {
    return delFeedback(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('feedback/user/export', {
    ...queryParams.value
  }, `feedback_${new Date().getTime()}.xlsx`)
}

/** 分配按钮操作 */
function handleAssign(row) {
  // 这里应该打开分配对话框，简化处理
  proxy.$modal.msgInfo("分配功能待实现");
}

/** 处理按钮操作 */
function handleProcess(row) {
  // 这里应该打开处理对话框，简化处理
  proxy.$modal.msgInfo("处理功能待实现");
}

/** 下拉菜单命令处理 */
function handleCommand(command, row) {
  switch (command) {
    case 'assign':
      handleAssign(row);
      break;
    case 'process':
      handleProcess(row);
      break;
    case 'close':
      proxy.$modal.msgInfo("关闭功能待实现");
      break;
    case 'reopen':
      proxy.$modal.msgInfo("重开功能待实现");
      break;
  }
}

onMounted(() => {
  getList();
  getCategoryOptions();
});
</script>
