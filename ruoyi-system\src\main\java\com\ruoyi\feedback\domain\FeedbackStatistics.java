package com.ruoyi.feedback.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 反馈统计对象 feedback_statistics
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class FeedbackStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 统计ID */
    private Long id;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statDate;

    /** 统计类型（daily日统计 weekly周统计 monthly月统计） */
    @Excel(name = "统计类型", readConverterExp = "daily=日统计,weekly=周统计,monthly=月统计")
    private String statType;

    /** 分类ID（为空表示全部） */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 总反馈数 */
    @Excel(name = "总反馈数")
    private Integer totalCount;

    /** 待处理数 */
    @Excel(name = "待处理数")
    private Integer pendingCount;

    /** 处理中数 */
    @Excel(name = "处理中数")
    private Integer processingCount;

    /** 已完成数 */
    @Excel(name = "已完成数")
    private Integer completedCount;

    /** 已关闭数 */
    @Excel(name = "已关闭数")
    private Integer closedCount;

    /** 平均评分 */
    @Excel(name = "平均评分")
    private BigDecimal avgRating;

    /** 平均处理时间（小时） */
    @Excel(name = "平均处理时间（小时）")
    private BigDecimal avgHandleTime;

    /** 平均满意度 */
    @Excel(name = "平均满意度")
    private BigDecimal satisfactionAvg;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStatDate(Date statDate) 
    {
        this.statDate = statDate;
    }

    public Date getStatDate() 
    {
        return statDate;
    }

    public void setStatType(String statType) 
    {
        this.statType = statType;
    }

    public String getStatType() 
    {
        return statType;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setTotalCount(Integer totalCount) 
    {
        this.totalCount = totalCount;
    }

    public Integer getTotalCount() 
    {
        return totalCount;
    }

    public void setPendingCount(Integer pendingCount) 
    {
        this.pendingCount = pendingCount;
    }

    public Integer getPendingCount() 
    {
        return pendingCount;
    }

    public void setProcessingCount(Integer processingCount) 
    {
        this.processingCount = processingCount;
    }

    public Integer getProcessingCount() 
    {
        return processingCount;
    }

    public void setCompletedCount(Integer completedCount) 
    {
        this.completedCount = completedCount;
    }

    public Integer getCompletedCount() 
    {
        return completedCount;
    }

    public void setClosedCount(Integer closedCount) 
    {
        this.closedCount = closedCount;
    }

    public Integer getClosedCount() 
    {
        return closedCount;
    }

    public void setAvgRating(BigDecimal avgRating) 
    {
        this.avgRating = avgRating;
    }

    public BigDecimal getAvgRating() 
    {
        return avgRating;
    }

    public void setAvgHandleTime(BigDecimal avgHandleTime) 
    {
        this.avgHandleTime = avgHandleTime;
    }

    public BigDecimal getAvgHandleTime() 
    {
        return avgHandleTime;
    }

    public void setSatisfactionAvg(BigDecimal satisfactionAvg) 
    {
        this.satisfactionAvg = satisfactionAvg;
    }

    public BigDecimal getSatisfactionAvg() 
    {
        return satisfactionAvg;
    }

    @Override
    public Date getUpdateTime() 
    {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) 
    {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("statDate", getStatDate())
            .append("statType", getStatType())
            .append("categoryId", getCategoryId())
            .append("totalCount", getTotalCount())
            .append("pendingCount", getPendingCount())
            .append("processingCount", getProcessingCount())
            .append("completedCount", getCompletedCount())
            .append("closedCount", getClosedCount())
            .append("avgRating", getAvgRating())
            .append("avgHandleTime", getAvgHandleTime())
            .append("satisfactionAvg", getSatisfactionAvg())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
