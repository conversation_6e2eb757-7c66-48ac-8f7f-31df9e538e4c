package com.ruoyi.web.controller.feedback;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.feedback.domain.KnowledgeOptimizationSuggestion;
import com.ruoyi.feedback.service.IKnowledgeOptimizationSuggestionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 知识库优化建议Controller
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@RestController
@RequestMapping("/feedback/suggestion")
public class KnowledgeOptimizationSuggestionController extends BaseController
{
    @Autowired
    private IKnowledgeOptimizationSuggestionService knowledgeOptimizationSuggestionService;

    /**
     * 查询知识库优化建议列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:list')")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectKnowledgeOptimizationSuggestionList(knowledgeOptimizationSuggestion);
        return getDataTable(list);
    }

    /**
     * 查询自动生成的优化建议列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:list')")
    @GetMapping("/auto")
    public TableDataInfo autoGeneratedList()
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectAutoGeneratedSuggestionList();
        return getDataTable(list);
    }

    /**
     * 查询待审核的优化建议列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:review')")
    @GetMapping("/pending")
    public TableDataInfo pendingReviewList()
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectPendingReviewSuggestionList();
        return getDataTable(list);
    }

    /**
     * 查询已通过待实施的优化建议列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:implement')")
    @GetMapping("/approved")
    public TableDataInfo approvedList()
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectApprovedSuggestionList();
        return getDataTable(list);
    }

    /**
     * 根据建议类型查询优化建议列表
     */
    @GetMapping("/type/{suggestionType}")
    public TableDataInfo getBySuggestionType(@PathVariable("suggestionType") String suggestionType)
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectSuggestionBySuggestionType(suggestionType);
        return getDataTable(list);
    }

    /**
     * 根据目标查询优化建议列表
     */
    @GetMapping("/target/{targetType}/{targetId}")
    public TableDataInfo getByTarget(@PathVariable("targetType") String targetType,
                                   @PathVariable("targetId") Long targetId)
    {
        startPage();
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectSuggestionByTarget(targetType, targetId);
        return getDataTable(list);
    }

    /**
     * 获取优化建议统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<Map<String, Object>> typeStats = knowledgeOptimizationSuggestionService.selectSuggestionCountByType();
        List<Map<String, Object>> statusStats = knowledgeOptimizationSuggestionService.selectSuggestionCountByStatus();
        
        return success()
            .put("typeStats", typeStats)
            .put("statusStats", statusStats);
    }

    /**
     * 导出知识库优化建议列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:export')")
    @Log(title = "知识库优化建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        List<KnowledgeOptimizationSuggestion> list = knowledgeOptimizationSuggestionService.selectKnowledgeOptimizationSuggestionList(knowledgeOptimizationSuggestion);
        ExcelUtil<KnowledgeOptimizationSuggestion> util = new ExcelUtil<KnowledgeOptimizationSuggestion>(KnowledgeOptimizationSuggestion.class);
        util.exportExcel(response, list, "知识库优化建议数据");
    }

    /**
     * 获取知识库优化建议详细信息
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeOptimizationSuggestionService.selectKnowledgeOptimizationSuggestionById(id));
    }

    /**
     * 新增知识库优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:add')")
    @Log(title = "知识库优化建议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        knowledgeOptimizationSuggestion.setCreateBy(getUsername());
        knowledgeOptimizationSuggestion.setAutoGenerated("0"); // 手动创建
        return toAjax(knowledgeOptimizationSuggestionService.insertKnowledgeOptimizationSuggestion(knowledgeOptimizationSuggestion));
    }

    /**
     * 修改知识库优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:edit')")
    @Log(title = "知识库优化建议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        knowledgeOptimizationSuggestion.setUpdateBy(getUsername());
        return toAjax(knowledgeOptimizationSuggestionService.updateKnowledgeOptimizationSuggestion(knowledgeOptimizationSuggestion));
    }

    /**
     * 审核优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:review')")
    @Log(title = "审核优化建议", businessType = BusinessType.UPDATE)
    @PutMapping("/review/{id}")
    public AjaxResult review(@PathVariable("id") Long id,
                           @RequestParam("status") String status,
                           @RequestParam(value = "reviewComment", required = false) String reviewComment)
    {
        return toAjax(knowledgeOptimizationSuggestionService.reviewSuggestion(id, status, reviewComment, getUserId(), getUsername()));
    }

    /**
     * 批量审核优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:review')")
    @Log(title = "批量审核优化建议", businessType = BusinessType.UPDATE)
    @PutMapping("/batchReview")
    public AjaxResult batchReview(@RequestParam("ids") Long[] ids,
                                @RequestParam("status") String status)
    {
        return toAjax(knowledgeOptimizationSuggestionService.batchReviewSuggestion(ids, status, getUserId(), getUsername()));
    }

    /**
     * 实施优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:implement')")
    @Log(title = "实施优化建议", businessType = BusinessType.UPDATE)
    @PutMapping("/implement/{id}")
    public AjaxResult implement(@PathVariable("id") Long id,
                              @RequestParam("implementResult") String implementResult)
    {
        return toAjax(knowledgeOptimizationSuggestionService.implementSuggestion(id, implementResult, getUserId(), getUsername()));
    }

    /**
     * 关闭优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:edit')")
    @Log(title = "关闭优化建议", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{id}")
    public AjaxResult close(@PathVariable("id") Long id,
                          @RequestParam("closeReason") String closeReason)
    {
        return toAjax(knowledgeOptimizationSuggestionService.closeSuggestion(id, closeReason));
    }

    /**
     * 评估优化建议效果
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:evaluate')")
    @Log(title = "评估优化建议效果", businessType = BusinessType.UPDATE)
    @PutMapping("/evaluate/{id}")
    public AjaxResult evaluate(@PathVariable("id") Long id,
                             @RequestParam("effectEvaluation") String effectEvaluation)
    {
        return toAjax(knowledgeOptimizationSuggestionService.evaluateSuggestionEffect(id, effectEvaluation));
    }

    /**
     * 基于反馈数据生成优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:generate')")
    @Log(title = "生成优化建议", businessType = BusinessType.INSERT)
    @PostMapping("/generateFromFeedback")
    public AjaxResult generateFromFeedback(@RequestParam("feedbackIds") List<Long> feedbackIds)
    {
        return toAjax(knowledgeOptimizationSuggestionService.generateSuggestionFromFeedback(feedbackIds));
    }

    /**
     * 自动生成知识库优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:generate')")
    @Log(title = "自动生成优化建议", businessType = BusinessType.INSERT)
    @PostMapping("/autoGenerate")
    public AjaxResult autoGenerate(@RequestParam(value = "knowledgeBaseId", required = false) Long knowledgeBaseId)
    {
        return toAjax(knowledgeOptimizationSuggestionService.autoGenerateOptimizationSuggestions(knowledgeBaseId));
    }

    /**
     * 删除知识库优化建议
     */
    @PreAuthorize("@ss.hasPermi('feedback:suggestion:remove')")
    @Log(title = "知识库优化建议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeOptimizationSuggestionService.deleteKnowledgeOptimizationSuggestionByIds(ids));
    }
}
