<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.feedback.mapper.FeedbackCategoryMapper">
    
    <resultMap type="FeedbackCategory" id="FeedbackCategoryResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="description"    column="description"    />
        <result property="icon"    column="icon"    />
        <result property="color"    column="color"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFeedbackCategoryVo">
        select id, name, code, description, icon, color, sort_order, status, create_by, create_time, update_by, update_time, remark from feedback_category
    </sql>

    <select id="selectFeedbackCategoryList" parameterType="FeedbackCategory" resultMap="FeedbackCategoryResult">
        <include refid="selectFeedbackCategoryVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>
    
    <select id="selectFeedbackCategoryById" parameterType="Long" resultMap="FeedbackCategoryResult">
        <include refid="selectFeedbackCategoryVo"/>
        where id = #{id}
    </select>

    <select id="selectFeedbackCategoryByCode" parameterType="String" resultMap="FeedbackCategoryResult">
        <include refid="selectFeedbackCategoryVo"/>
        where code = #{code}
    </select>

    <select id="selectEnabledFeedbackCategoryList" resultMap="FeedbackCategoryResult">
        <include refid="selectFeedbackCategoryVo"/>
        where status = '0'
        order by sort_order asc
    </select>

    <select id="checkCodeUnique" parameterType="FeedbackCategory" resultMap="FeedbackCategoryResult">
        <include refid="selectFeedbackCategoryVo"/>
        where code = #{code} 
        <if test="id != null and id != 0">
            and id != #{id}
        </if>
        limit 1
    </select>
        
    <insert id="insertFeedbackCategory" parameterType="FeedbackCategory" useGeneratedKeys="true" keyProperty="id">
        insert into feedback_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="description != null">description,</if>
            <if test="icon != null">icon,</if>
            <if test="color != null">color,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="icon != null">#{icon},</if>
            <if test="color != null">#{color},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateFeedbackCategory" parameterType="FeedbackCategory">
        update feedback_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="color != null">color = #{color},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeedbackCategoryById" parameterType="Long">
        delete from feedback_category where id = #{id}
    </delete>

    <delete id="deleteFeedbackCategoryByIds" parameterType="String">
        delete from feedback_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
