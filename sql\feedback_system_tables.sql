-- =============================================
-- 反馈系统数据库表结构
-- 创建时间: 2025-08-29
-- 描述: 用户反馈采集和知识库优化建议系统
-- =============================================

USE `ry-vue`;

-- =============================================
-- 1. 反馈分类表
-- =============================================
DROP TABLE IF EXISTS `feedback_category`;
CREATE TABLE `feedback_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(30) NOT NULL COMMENT '分类编码',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(50) DEFAULT NULL COMMENT '分类图标',
  `color` varchar(20) DEFAULT NULL COMMENT '分类颜色',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈分类表';

-- =============================================
-- 2. 用户反馈表（简化版）
-- =============================================
DROP TABLE IF EXISTS `user_feedback`;
CREATE TABLE `user_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `title` varchar(200) NOT NULL COMMENT '反馈标题',
  `content` longtext NOT NULL COMMENT '反馈内容',
  `category_id` bigint NOT NULL COMMENT '反馈分类ID',
  `category_name` varchar(50) DEFAULT NULL COMMENT '分类名称（冗余字段）',
  `rating` tinyint DEFAULT NULL COMMENT '评分（1-5分）',
  `user_id` bigint NOT NULL COMMENT '反馈用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户名',
  `knowledge_base_id` bigint DEFAULT NULL COMMENT '关联知识库ID',
  `knowledge_base_name` varchar(100) DEFAULT NULL COMMENT '知识库名称（冗余字段）',
  `document_id` bigint DEFAULT NULL COMMENT '关联文档ID',
  `document_name` varchar(200) DEFAULT NULL COMMENT '文档名称（冗余字段）',
  `ai_session_id` varchar(64) DEFAULT NULL COMMENT '关联AI会话ID',
  `source_type` varchar(20) DEFAULT 'manual' COMMENT '反馈来源（manual用户手动提交 auto系统自动收集 system管理员创建）',
  `source_page` varchar(100) DEFAULT NULL COMMENT '反馈来源页面',
  `status` char(1) DEFAULT '0' COMMENT '处理状态（0待处理 1处理中 2已处理 3已关闭）',
  `handler_id` bigint DEFAULT NULL COMMENT '处理人ID',
  `handler_name` varchar(30) DEFAULT NULL COMMENT '处理人姓名',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_result` text DEFAULT NULL COMMENT '处理结果',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_ai_session_id` (`ai_session_id`),
  KEY `idx_status` (`status`),
  KEY `idx_source_type` (`source_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_handler_id` (`handler_id`),
  FULLTEXT KEY `idx_content` (`title`,`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表';

-- =============================================
-- 3. 知识库优化建议表
-- =============================================
DROP TABLE IF EXISTS `knowledge_optimization_suggestion`;
CREATE TABLE `knowledge_optimization_suggestion` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '建议ID',
  `title` varchar(200) NOT NULL COMMENT '建议标题',
  `description` text NOT NULL COMMENT '建议描述',
  `suggestion_type` varchar(30) NOT NULL COMMENT '建议类型（content内容优化 structure结构调整 tag标签完善 search检索优化）',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型（knowledge_base知识库 document文档 category分类）',
  `target_id` bigint NOT NULL COMMENT '目标ID',
  `target_name` varchar(200) DEFAULT NULL COMMENT '目标名称（冗余字段）',
  `current_content` longtext DEFAULT NULL COMMENT '当前内容',
  `suggested_content` longtext DEFAULT NULL COMMENT '建议内容',
  `optimization_reason` text DEFAULT NULL COMMENT '优化原因',
  `expected_effect` text DEFAULT NULL COMMENT '预期效果',
  `priority` char(1) DEFAULT '2' COMMENT '优先级（1高 2中 3低）',
  `confidence_score` decimal(3,2) DEFAULT NULL COMMENT '置信度评分（0.00-1.00）',
  `source_feedback_ids` text DEFAULT NULL COMMENT '来源反馈ID列表（JSON格式）',
  `feedback_count` int DEFAULT '0' COMMENT '相关反馈数量',
  `auto_generated` char(1) DEFAULT '0' COMMENT '是否自动生成（0否 1是）',
  `algorithm_version` varchar(20) DEFAULT NULL COMMENT '算法版本',
  `status` char(1) DEFAULT '0' COMMENT '状态（0待审核 1已通过 2已拒绝 3已实施 4已关闭）',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `reviewer_name` varchar(30) DEFAULT NULL COMMENT '审核人姓名',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text DEFAULT NULL COMMENT '审核意见',
  `implementer_id` bigint DEFAULT NULL COMMENT '实施人ID',
  `implementer_name` varchar(30) DEFAULT NULL COMMENT '实施人姓名',
  `implement_time` datetime DEFAULT NULL COMMENT '实施时间',
  `implement_result` text DEFAULT NULL COMMENT '实施结果',
  `effect_evaluation` text DEFAULT NULL COMMENT '效果评估',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_suggestion_type` (`suggestion_type`),
  KEY `idx_target_type_id` (`target_type`,`target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_auto_generated` (`auto_generated`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_reviewer_id` (`reviewer_id`),
  KEY `idx_implementer_id` (`implementer_id`),
  FULLTEXT KEY `idx_content` (`title`,`description`,`suggested_content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库优化建议表';

-- =============================================
-- 4. 反馈处理记录表
-- =============================================
DROP TABLE IF EXISTS `feedback_process_log`;
CREATE TABLE `feedback_process_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `feedback_id` bigint NOT NULL COMMENT '反馈ID',
  `action_type` varchar(30) NOT NULL COMMENT '操作类型（create创建 assign分配 process处理 close关闭 reopen重开）',
  `action_description` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `old_status` char(1) DEFAULT NULL COMMENT '原状态',
  `new_status` char(1) DEFAULT NULL COMMENT '新状态',
  `old_handler_id` bigint DEFAULT NULL COMMENT '原处理人ID',
  `new_handler_id` bigint DEFAULT NULL COMMENT '新处理人ID',
  `process_content` text DEFAULT NULL COMMENT '处理内容',
  `attachments` text DEFAULT NULL COMMENT '附件信息（JSON格式）',
  `time_spent` int DEFAULT NULL COMMENT '耗时（分钟）',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(30) NOT NULL COMMENT '操作人姓名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_feedback_id` (`feedback_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈处理记录表';

-- =============================================
-- 5. 反馈统计表
-- =============================================
DROP TABLE IF EXISTS `feedback_statistics`;
CREATE TABLE `feedback_statistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` varchar(20) NOT NULL COMMENT '统计类型（daily日统计 weekly周统计 monthly月统计）',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID（为空表示全部）',
  `total_count` int DEFAULT '0' COMMENT '总反馈数',
  `pending_count` int DEFAULT '0' COMMENT '待处理数',
  `processing_count` int DEFAULT '0' COMMENT '处理中数',
  `completed_count` int DEFAULT '0' COMMENT '已完成数',
  `closed_count` int DEFAULT '0' COMMENT '已关闭数',
  `avg_rating` decimal(3,2) DEFAULT NULL COMMENT '平均评分',
  `avg_handle_time` decimal(8,2) DEFAULT NULL COMMENT '平均处理时间（小时）',
  `satisfaction_avg` decimal(3,2) DEFAULT NULL COMMENT '平均满意度',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date_type_category` (`stat_date`,`stat_type`,`category_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_stat_type` (`stat_type`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈统计表';

-- =============================================
-- 初始化反馈分类数据
-- =============================================
INSERT INTO `feedback_category` (`name`, `code`, `description`, `icon`, `color`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('内容问题', 'content_issue', '知识库内容相关问题', 'el-icon-document', '#E6A23C', 1, '0', 'system', NOW()),
('搜索问题', 'search_issue', '搜索功能相关问题', 'el-icon-search', '#409EFF', 2, '0', 'system', NOW()),
('AI问答问题', 'ai_issue', 'AI智能问答相关问题', 'el-icon-chat-dot-round', '#67C23A', 3, '0', 'system', NOW()),
('界面问题', 'ui_issue', '用户界面相关问题', 'el-icon-monitor', '#909399', 4, '0', 'system', NOW()),
('功能建议', 'feature_request', '新功能建议', 'el-icon-plus', '#67C23A', 5, '0', 'system', NOW()),
('性能问题', 'performance_issue', '系统性能相关问题', 'el-icon-timer', '#F56C6C', 6, '0', 'system', NOW()),
('其他问题', 'other_issue', '其他类型问题', 'el-icon-more', '#909399', 99, '0', 'system', NOW());

-- =============================================
-- 创建相关索引优化
-- =============================================

-- 为用户反馈表创建复合索引
ALTER TABLE `user_feedback` ADD INDEX `idx_status_create_time` (`status`, `create_time`);
ALTER TABLE `user_feedback` ADD INDEX `idx_user_category_time` (`user_id`, `category_id`, `create_time`);
ALTER TABLE `user_feedback` ADD INDEX `idx_knowledge_base_status` (`knowledge_base_id`, `status`);

-- 为优化建议表创建复合索引
ALTER TABLE `knowledge_optimization_suggestion` ADD INDEX `idx_status_priority_time` (`status`, `priority`, `create_time`);
ALTER TABLE `knowledge_optimization_suggestion` ADD INDEX `idx_target_status` (`target_type`, `target_id`, `status`);
ALTER TABLE `knowledge_optimization_suggestion` ADD INDEX `idx_auto_generated_status` (`auto_generated`, `status`);

-- =============================================
-- 创建视图便于查询
-- =============================================

-- 反馈概览视图
CREATE OR REPLACE VIEW `v_feedback_overview` AS
SELECT
    f.id,
    f.title,
    f.rating,
    f.status,
    f.user_name,
    f.source_type,
    f.create_time,
    c.name AS category_name,
    c.color AS category_color,
    CASE
        WHEN f.knowledge_base_id IS NOT NULL THEN '知识库'
        WHEN f.document_id IS NOT NULL THEN '文档'
        WHEN f.ai_session_id IS NOT NULL THEN 'AI对话'
        ELSE '其他'
    END AS source_type_name,
    COALESCE(f.knowledge_base_name, f.document_name, 'AI对话') AS source_name
FROM user_feedback f
LEFT JOIN feedback_category c ON f.category_id = c.id
WHERE f.status != '3';

-- 优化建议概览视图
CREATE OR REPLACE VIEW `v_optimization_suggestion_overview` AS
SELECT 
    s.id,
    s.title,
    s.suggestion_type,
    s.target_type,
    s.target_name,
    s.priority,
    s.confidence_score,
    s.feedback_count,
    s.status,
    s.auto_generated,
    s.create_time,
    s.reviewer_name,
    s.implementer_name
FROM knowledge_optimization_suggestion s
WHERE s.status != '4';
