package com.ruoyi.web.controller.feedback;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.feedback.domain.FeedbackProcessLog;
import com.ruoyi.feedback.service.IFeedbackProcessLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 反馈处理记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@RestController
@RequestMapping("/feedback/processLog")
public class FeedbackProcessLogController extends BaseController
{
    @Autowired
    private IFeedbackProcessLogService feedbackProcessLogService;

    /**
     * 查询反馈处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(FeedbackProcessLog feedbackProcessLog)
    {
        startPage();
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogList(feedbackProcessLog);
        return getDataTable(list);
    }

    /**
     * 根据反馈ID查询处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:list')")
    @GetMapping("/feedback/{feedbackId}")
    public TableDataInfo getByFeedbackId(@PathVariable("feedbackId") Long feedbackId)
    {
        startPage();
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogByFeedbackId(feedbackId);
        return getDataTable(list);
    }

    /**
     * 根据操作类型查询处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:list')")
    @GetMapping("/action/{actionType}")
    public TableDataInfo getByActionType(@PathVariable("actionType") String actionType)
    {
        startPage();
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogByActionType(actionType);
        return getDataTable(list);
    }

    /**
     * 根据操作人ID查询处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:list')")
    @GetMapping("/operator/{operatorId}")
    public TableDataInfo getByOperatorId(@PathVariable("operatorId") Long operatorId)
    {
        startPage();
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogByOperatorId(operatorId);
        return getDataTable(list);
    }

    /**
     * 查询我的操作记录
     */
    @GetMapping("/my")
    public TableDataInfo myProcessLogs(FeedbackProcessLog feedbackProcessLog)
    {
        startPage();
        feedbackProcessLog.setOperatorId(getUserId());
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogList(feedbackProcessLog);
        return getDataTable(list);
    }

    /**
     * 导出反馈处理记录列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:export')")
    @Log(title = "反馈处理记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FeedbackProcessLog feedbackProcessLog)
    {
        List<FeedbackProcessLog> list = feedbackProcessLogService.selectFeedbackProcessLogList(feedbackProcessLog);
        ExcelUtil<FeedbackProcessLog> util = new ExcelUtil<FeedbackProcessLog>(FeedbackProcessLog.class);
        util.exportExcel(response, list, "反馈处理记录数据");
    }

    /**
     * 获取反馈处理记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(feedbackProcessLogService.selectFeedbackProcessLogById(id));
    }

    /**
     * 新增反馈处理记录
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:add')")
    @Log(title = "反馈处理记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FeedbackProcessLog feedbackProcessLog)
    {
        feedbackProcessLog.setOperatorId(getUserId());
        feedbackProcessLog.setOperatorName(getUsername());
        return toAjax(feedbackProcessLogService.insertFeedbackProcessLog(feedbackProcessLog));
    }

    /**
     * 修改反馈处理记录
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:edit')")
    @Log(title = "反馈处理记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FeedbackProcessLog feedbackProcessLog)
    {
        return toAjax(feedbackProcessLogService.updateFeedbackProcessLog(feedbackProcessLog));
    }

    /**
     * 删除反馈处理记录
     */
    @PreAuthorize("@ss.hasPermi('feedback:processLog:remove')")
    @Log(title = "反馈处理记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(feedbackProcessLogService.deleteFeedbackProcessLogByIds(ids));
    }
}
