<template>
  <div class="app-container">
    <!-- 统计概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>总反馈数</span>
            <el-icon class="card-icon"><Document /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-number">{{ overviewData.totalCount || 0 }}</div>
            <div class="card-desc">累计反馈总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>待处理</span>
            <el-icon class="card-icon"><Clock /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-number pending">{{ overviewData.pendingCount || 0 }}</div>
            <div class="card-desc">待处理反馈数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>处理中</span>
            <el-icon class="card-icon"><Loading /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-number processing">{{ overviewData.processingCount || 0 }}</div>
            <div class="card-desc">处理中反馈数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-header">
            <span>已完成</span>
            <el-icon class="card-icon"><Check /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-number completed">{{ overviewData.completedCount || 0 }}</div>
            <div class="card-desc">已完成反馈数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-card class="mb20">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
        <el-form-item label="统计类型" prop="statType">
          <el-select v-model="queryParams.statType" placeholder="请选择统计类型" @change="handleStatTypeChange">
            <el-option label="日统计" value="daily" />
            <el-option label="周统计" value="weekly" />
            <el-option label="月统计" value="monthly" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="queryParams.startDate"
            type="date"
            placeholder="选择开始日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="queryParams.endDate"
            type="date"
            placeholder="选择结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表展示 -->
    <el-row :gutter="20">
      <!-- 反馈趋势图 -->
      <el-col :span="12">
        <el-card class="mb20">
          <template #header>
            <div class="card-header">
              <span>反馈趋势分析</span>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 反馈分类分布 -->
      <el-col :span="12">
        <el-card class="mb20">
          <template #header>
            <div class="card-header">
              <span>反馈分类分布</span>
            </div>
          </template>
          <div ref="categoryChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 反馈状态分布 -->
      <el-col :span="12">
        <el-card class="mb20">
          <template #header>
            <div class="card-header">
              <span>反馈状态分布</span>
            </div>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 处理效率分析 -->
      <el-col :span="12">
        <el-card class="mb20">
          <template #header>
            <div class="card-header">
              <span>处理效率分析</span>
            </div>
          </template>
          <div class="efficiency-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="efficiency-item">
                  <div class="efficiency-label">平均处理时间</div>
                  <div class="efficiency-value">{{ efficiencyData.avgProcessingTime || 0 }} 小时</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="efficiency-item">
                  <div class="efficiency-label">处理效率</div>
                  <div class="efficiency-value">{{ efficiencyData.efficiency || 0 }}%</div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt20">
              <el-col :span="12">
                <div class="efficiency-item">
                  <div class="efficiency-label">已处理数量</div>
                  <div class="efficiency-value">{{ efficiencyData.processedCount || 0 }}</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="efficiency-item">
                  <div class="efficiency-label">待处理数量</div>
                  <div class="efficiency-value">{{ efficiencyData.pendingCount || 0 }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 满意度分析 -->
    <el-row>
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>满意度分析</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="satisfaction-item">
                <div class="satisfaction-label">平均评分</div>
                <div class="satisfaction-value">
                  <el-rate v-model="satisfactionData.avgRating" disabled show-score text-color="#ff9900" />
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="satisfaction-item">
                <div class="satisfaction-label">满意度</div>
                <div class="satisfaction-value">{{ satisfactionData.satisfactionRate || 0 }}%</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="satisfaction-item">
                <div class="satisfaction-label">总评分数量</div>
                <div class="satisfaction-value">{{ satisfactionData.totalRatings || 0 }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="FeedbackStatistics">
import * as echarts from 'echarts';
import { getOverviewData, getTrendData, getCategoryDistribution, getStatusDistribution, getProcessingEfficiency, getSatisfactionData } from "@/api/feedback/statistics";

const { proxy } = getCurrentInstance();

const trendChartRef = ref();
const categoryChartRef = ref();
const statusChartRef = ref();

const overviewData = ref({});
const efficiencyData = ref({});
const satisfactionData = ref({});

let trendChart = null;
let categoryChart = null;
let statusChart = null;

const data = reactive({
  queryParams: {
    statType: 'daily',
    startDate: null,
    endDate: null
  }
});

const { queryParams } = toRefs(data);

/** 获取概览数据 */
function getOverview() {
  getOverviewData().then(response => {
    overviewData.value = response.data;
  });
}

/** 获取处理效率数据 */
function getEfficiency() {
  const params = {
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate
  };
  getProcessingEfficiency(params).then(response => {
    efficiencyData.value = response.data;
  });
}

/** 获取满意度数据 */
function getSatisfaction() {
  const params = {
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate
  };
  getSatisfactionData(params).then(response => {
    satisfactionData.value = response.data;
  });
}

/** 初始化趋势图表 */
function initTrendChart() {
  if (trendChart) {
    trendChart.dispose();
  }
  trendChart = echarts.init(trendChartRef.value);
  
  const params = {
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate,
    statType: queryParams.value.statType
  };
  
  getTrendData(params).then(response => {
    const data = response.data || [];
    const dates = data.map(item => item.statDate);
    const totals = data.map(item => item.totalCount);
    const pendings = data.map(item => item.pendingCount);
    const completeds = data.map(item => item.completedCount);
    
    const option = {
      title: {
        text: '反馈趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['总数', '待处理', '已完成']
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '总数',
          type: 'line',
          data: totals
        },
        {
          name: '待处理',
          type: 'line',
          data: pendings
        },
        {
          name: '已完成',
          type: 'line',
          data: completeds
        }
      ]
    };
    
    trendChart.setOption(option);
  });
}

/** 初始化分类分布图表 */
function initCategoryChart() {
  if (categoryChart) {
    categoryChart.dispose();
  }
  categoryChart = echarts.init(categoryChartRef.value);
  
  const params = {
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate
  };
  
  getCategoryDistribution(params).then(response => {
    const data = response.data || [];
    const chartData = data.map(item => ({
      name: item.categoryName,
      value: item.count
    }));
    
    const option = {
      title: {
        text: '分类分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '反馈分类',
          type: 'pie',
          radius: '50%',
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    categoryChart.setOption(option);
  });
}

/** 初始化状态分布图表 */
function initStatusChart() {
  if (statusChart) {
    statusChart.dispose();
  }
  statusChart = echarts.init(statusChartRef.value);
  
  const params = {
    startDate: queryParams.value.startDate,
    endDate: queryParams.value.endDate
  };
  
  getStatusDistribution(params).then(response => {
    const data = response.data || [];
    const statusMap = {
      '0': '待处理',
      '1': '处理中',
      '2': '已处理',
      '3': '已关闭'
    };
    
    const chartData = data.map(item => ({
      name: statusMap[item.status] || item.status,
      value: item.count
    }));
    
    const option = {
      title: {
        text: '状态分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '反馈状态',
          type: 'pie',
          radius: ['40%', '70%'],
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    statusChart.setOption(option);
  });
}

/** 初始化所有图表 */
function initCharts() {
  nextTick(() => {
    initTrendChart();
    initCategoryChart();
    initStatusChart();
  });
}

/** 统计类型变更处理 */
function handleStatTypeChange() {
  initTrendChart();
}

/** 查询按钮操作 */
function handleQuery() {
  initCharts();
  getEfficiency();
  getSatisfaction();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 设置默认日期范围（最近30天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);
  
  queryParams.value.startDate = startDate.toISOString().split('T')[0];
  queryParams.value.endDate = endDate.toISOString().split('T')[0];
  
  handleQuery();
}

/** 窗口大小变化处理 */
function handleResize() {
  if (trendChart) trendChart.resize();
  if (categoryChart) categoryChart.resize();
  if (statusChart) statusChart.resize();
}

onMounted(() => {
  // 设置默认日期范围
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);
  
  queryParams.value.startDate = startDate.toISOString().split('T')[0];
  queryParams.value.endDate = endDate.toISOString().split('T')[0];
  
  getOverview();
  initCharts();
  getEfficiency();
  getSatisfaction();
  
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (trendChart) trendChart.dispose();
  if (categoryChart) categoryChart.dispose();
  if (statusChart) statusChart.dispose();
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.box-card {
  height: 120px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.card-content {
  text-align: center;
  padding: 10px 0;
}

.card-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.card-number.pending {
  color: #e6a23c;
}

.card-number.processing {
  color: #409eff;
}

.card-number.completed {
  color: #67c23a;
}

.card-desc {
  font-size: 14px;
  color: #909399;
}

.efficiency-content {
  padding: 20px;
}

.efficiency-item {
  text-align: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.efficiency-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.efficiency-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.satisfaction-item {
  text-align: center;
  padding: 20px;
}

.satisfaction-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 15px;
}

.satisfaction-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>
