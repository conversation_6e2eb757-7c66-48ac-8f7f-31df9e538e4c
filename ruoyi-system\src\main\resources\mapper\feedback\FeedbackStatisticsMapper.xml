<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.feedback.mapper.FeedbackStatisticsMapper">
    
    <resultMap type="FeedbackStatistics" id="FeedbackStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statDate"    column="stat_date"    />
        <result property="statType"    column="stat_type"    />
        <result property="categoryId"    column="category_id"    />
        <result property="totalCount"    column="total_count"    />
        <result property="pendingCount"    column="pending_count"    />
        <result property="processingCount"    column="processing_count"    />
        <result property="completedCount"    column="completed_count"    />
        <result property="closedCount"    column="closed_count"    />
        <result property="avgRating"    column="avg_rating"    />
        <result property="avgHandleTime"    column="avg_handle_time"    />
        <result property="satisfactionAvg"    column="satisfaction_avg"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFeedbackStatisticsVo">
        select id, stat_date, stat_type, category_id, total_count, pending_count, processing_count, completed_count, closed_count, avg_rating, avg_handle_time, satisfaction_avg, create_time, update_time from feedback_statistics
    </sql>

    <select id="selectFeedbackStatisticsList" parameterType="FeedbackStatistics" resultMap="FeedbackStatisticsResult">
        <include refid="selectFeedbackStatisticsVo"/>
        <where>  
            <if test="statDate != null "> and stat_date = #{statDate}</if>
            <if test="statType != null  and statType != ''"> and stat_type = #{statType}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
        </where>
        order by stat_date desc, stat_type, category_id
    </select>
    
    <select id="selectFeedbackStatisticsById" parameterType="Long" resultMap="FeedbackStatisticsResult">
        <include refid="selectFeedbackStatisticsVo"/>
        where id = #{id}
    </select>

    <select id="selectFeedbackStatisticsByDateAndType" resultMap="FeedbackStatisticsResult">
        <include refid="selectFeedbackStatisticsVo"/>
        where stat_date = #{statDate} and stat_type = #{statType}
        <if test="categoryId != null">
            and category_id = #{categoryId}
        </if>
        <if test="categoryId == null">
            and category_id is null
        </if>
    </select>

    <select id="selectFeedbackStatisticsByDateRange" resultMap="FeedbackStatisticsResult">
        <include refid="selectFeedbackStatisticsVo"/>
        where stat_date between #{startDate} and #{endDate}
        and stat_type = #{statType}
        order by stat_date desc, category_id
    </select>
        
    <insert id="insertFeedbackStatistics" parameterType="FeedbackStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into feedback_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statDate != null">stat_date,</if>
            <if test="statType != null and statType != ''">stat_type,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="pendingCount != null">pending_count,</if>
            <if test="processingCount != null">processing_count,</if>
            <if test="completedCount != null">completed_count,</if>
            <if test="closedCount != null">closed_count,</if>
            <if test="avgRating != null">avg_rating,</if>
            <if test="avgHandleTime != null">avg_handle_time,</if>
            <if test="satisfactionAvg != null">satisfaction_avg,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statDate != null">#{statDate},</if>
            <if test="statType != null and statType != ''">#{statType},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="pendingCount != null">#{pendingCount},</if>
            <if test="processingCount != null">#{processingCount},</if>
            <if test="completedCount != null">#{completedCount},</if>
            <if test="closedCount != null">#{closedCount},</if>
            <if test="avgRating != null">#{avgRating},</if>
            <if test="avgHandleTime != null">#{avgHandleTime},</if>
            <if test="satisfactionAvg != null">#{satisfactionAvg},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateFeedbackStatistics" parameterType="FeedbackStatistics">
        update feedback_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="pendingCount != null">pending_count = #{pendingCount},</if>
            <if test="processingCount != null">processing_count = #{processingCount},</if>
            <if test="completedCount != null">completed_count = #{completedCount},</if>
            <if test="closedCount != null">closed_count = #{closedCount},</if>
            <if test="avgRating != null">avg_rating = #{avgRating},</if>
            <if test="avgHandleTime != null">avg_handle_time = #{avgHandleTime},</if>
            <if test="satisfactionAvg != null">satisfaction_avg = #{satisfactionAvg},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFeedbackStatisticsById" parameterType="Long">
        delete from feedback_statistics where id = #{id}
    </delete>

    <delete id="deleteFeedbackStatisticsByIds" parameterType="String">
        delete from feedback_statistics where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteFeedbackStatisticsByDateRange">
        delete from feedback_statistics 
        where stat_date between #{startDate} and #{endDate}
    </delete>

</mapper>
