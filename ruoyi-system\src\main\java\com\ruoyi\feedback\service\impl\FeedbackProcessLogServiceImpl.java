package com.ruoyi.feedback.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.feedback.mapper.FeedbackProcessLogMapper;
import com.ruoyi.feedback.domain.FeedbackProcessLog;
import com.ruoyi.feedback.service.IFeedbackProcessLogService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 反馈处理记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Service
public class FeedbackProcessLogServiceImpl implements IFeedbackProcessLogService 
{
    @Autowired
    private FeedbackProcessLogMapper feedbackProcessLogMapper;

    /**
     * 查询反馈处理记录
     * 
     * @param id 反馈处理记录主键
     * @return 反馈处理记录
     */
    @Override
    public FeedbackProcessLog selectFeedbackProcessLogById(Long id)
    {
        return feedbackProcessLogMapper.selectFeedbackProcessLogById(id);
    }

    /**
     * 查询反馈处理记录列表
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 反馈处理记录
     */
    @Override
    public List<FeedbackProcessLog> selectFeedbackProcessLogList(FeedbackProcessLog feedbackProcessLog)
    {
        return feedbackProcessLogMapper.selectFeedbackProcessLogList(feedbackProcessLog);
    }

    /**
     * 根据反馈ID查询处理记录列表
     * 
     * @param feedbackId 反馈ID
     * @return 反馈处理记录集合
     */
    @Override
    public List<FeedbackProcessLog> selectFeedbackProcessLogByFeedbackId(Long feedbackId)
    {
        return feedbackProcessLogMapper.selectFeedbackProcessLogByFeedbackId(feedbackId);
    }

    /**
     * 根据操作类型查询处理记录列表
     * 
     * @param actionType 操作类型
     * @return 反馈处理记录集合
     */
    @Override
    public List<FeedbackProcessLog> selectFeedbackProcessLogByActionType(String actionType)
    {
        return feedbackProcessLogMapper.selectFeedbackProcessLogByActionType(actionType);
    }

    /**
     * 根据操作人ID查询处理记录列表
     * 
     * @param operatorId 操作人ID
     * @return 反馈处理记录集合
     */
    @Override
    public List<FeedbackProcessLog> selectFeedbackProcessLogByOperatorId(Long operatorId)
    {
        return feedbackProcessLogMapper.selectFeedbackProcessLogByOperatorId(operatorId);
    }

    /**
     * 新增反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    @Override
    public int insertFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog)
    {
        feedbackProcessLog.setCreateTime(DateUtils.getNowDate());
        return feedbackProcessLogMapper.insertFeedbackProcessLog(feedbackProcessLog);
    }

    /**
     * 修改反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    @Override
    public int updateFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog)
    {
        return feedbackProcessLogMapper.updateFeedbackProcessLog(feedbackProcessLog);
    }

    /**
     * 批量删除反馈处理记录
     * 
     * @param ids 需要删除的反馈处理记录主键
     * @return 结果
     */
    @Override
    public int deleteFeedbackProcessLogByIds(Long[] ids)
    {
        return feedbackProcessLogMapper.deleteFeedbackProcessLogByIds(ids);
    }

    /**
     * 删除反馈处理记录信息
     * 
     * @param id 反馈处理记录主键
     * @return 结果
     */
    @Override
    public int deleteFeedbackProcessLogById(Long id)
    {
        return feedbackProcessLogMapper.deleteFeedbackProcessLogById(id);
    }

    /**
     * 记录反馈创建操作
     * 
     * @param feedbackId 反馈ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackCreate(Long feedbackId, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("create");
        log.setActionDescription("创建反馈");
        log.setNewStatus("0"); // 待处理
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }

    /**
     * 记录反馈分配操作
     * 
     * @param feedbackId 反馈ID
     * @param oldHandlerId 原处理人ID
     * @param newHandlerId 新处理人ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackAssign(Long feedbackId, Long oldHandlerId, Long newHandlerId, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("assign");
        log.setActionDescription("分配反馈处理人");
        log.setOldStatus("0"); // 待处理
        log.setNewStatus("1"); // 处理中
        log.setOldHandlerId(oldHandlerId);
        log.setNewHandlerId(newHandlerId);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }

    /**
     * 记录反馈处理操作
     * 
     * @param feedbackId 反馈ID
     * @param processContent 处理内容
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackProcess(Long feedbackId, String processContent, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("process");
        log.setActionDescription("处理反馈");
        log.setOldStatus("1"); // 处理中
        log.setNewStatus("2"); // 已处理
        log.setProcessContent(processContent);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }

    /**
     * 记录反馈关闭操作
     * 
     * @param feedbackId 反馈ID
     * @param closeReason 关闭原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackClose(Long feedbackId, String closeReason, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("close");
        log.setActionDescription("关闭反馈");
        log.setNewStatus("3"); // 已关闭
        log.setProcessContent(closeReason);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }

    /**
     * 记录反馈重开操作
     * 
     * @param feedbackId 反馈ID
     * @param reopenReason 重开原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackReopen(Long feedbackId, String reopenReason, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("reopen");
        log.setActionDescription("重新打开反馈");
        log.setOldStatus("3"); // 已关闭
        log.setNewStatus("0"); // 待处理
        log.setProcessContent(reopenReason);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }

    /**
     * 记录反馈状态变更操作
     * 
     * @param feedbackId 反馈ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param actionDescription 操作描述
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    @Override
    public int logFeedbackStatusChange(Long feedbackId, String oldStatus, String newStatus, String actionDescription, Long operatorId, String operatorName)
    {
        FeedbackProcessLog log = new FeedbackProcessLog();
        log.setFeedbackId(feedbackId);
        log.setActionType("status_change");
        log.setActionDescription(actionDescription);
        log.setOldStatus(oldStatus);
        log.setNewStatus(newStatus);
        log.setOperatorId(operatorId);
        log.setOperatorName(operatorName);
        log.setCreateTime(DateUtils.getNowDate());
        
        return feedbackProcessLogMapper.insertFeedbackProcessLog(log);
    }
}
