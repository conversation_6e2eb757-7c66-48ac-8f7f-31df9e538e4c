package com.ruoyi.feedback.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.feedback.domain.FeedbackStatistics;

/**
 * 反馈统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface FeedbackStatisticsMapper 
{
    /**
     * 查询反馈统计
     * 
     * @param id 反馈统计主键
     * @return 反馈统计
     */
    public FeedbackStatistics selectFeedbackStatisticsById(Long id);

    /**
     * 查询反馈统计列表
     * 
     * @param feedbackStatistics 反馈统计
     * @return 反馈统计集合
     */
    public List<FeedbackStatistics> selectFeedbackStatisticsList(FeedbackStatistics feedbackStatistics);

    /**
     * 根据统计日期和类型查询统计数据
     * 
     * @param statDate 统计日期
     * @param statType 统计类型
     * @param categoryId 分类ID（可为空）
     * @return 反馈统计
     */
    public FeedbackStatistics selectFeedbackStatisticsByDateAndType(Date statDate, String statType, Long categoryId);

    /**
     * 根据日期范围查询统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statType 统计类型
     * @return 反馈统计集合
     */
    public List<FeedbackStatistics> selectFeedbackStatisticsByDateRange(Date startDate, Date endDate, String statType);

    /**
     * 新增反馈统计
     * 
     * @param feedbackStatistics 反馈统计
     * @return 结果
     */
    public int insertFeedbackStatistics(FeedbackStatistics feedbackStatistics);

    /**
     * 修改反馈统计
     * 
     * @param feedbackStatistics 反馈统计
     * @return 结果
     */
    public int updateFeedbackStatistics(FeedbackStatistics feedbackStatistics);

    /**
     * 删除反馈统计
     * 
     * @param id 反馈统计主键
     * @return 结果
     */
    public int deleteFeedbackStatisticsById(Long id);

    /**
     * 批量删除反馈统计
     * 
     * @param ids 需要删除的反馈统计主键集合
     * @return 结果
     */
    public int deleteFeedbackStatisticsByIds(Long[] ids);

    /**
     * 根据日期范围删除统计数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    public int deleteFeedbackStatisticsByDateRange(Date startDate, Date endDate);
}
