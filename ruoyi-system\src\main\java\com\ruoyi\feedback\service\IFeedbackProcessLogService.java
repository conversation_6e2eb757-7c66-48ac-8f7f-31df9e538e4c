package com.ruoyi.feedback.service;

import java.util.List;
import com.ruoyi.feedback.domain.FeedbackProcessLog;

/**
 * 反馈处理记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IFeedbackProcessLogService 
{
    /**
     * 查询反馈处理记录
     * 
     * @param id 反馈处理记录主键
     * @return 反馈处理记录
     */
    public FeedbackProcessLog selectFeedbackProcessLogById(Long id);

    /**
     * 查询反馈处理记录列表
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogList(FeedbackProcessLog feedbackProcessLog);

    /**
     * 根据反馈ID查询处理记录列表
     * 
     * @param feedbackId 反馈ID
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByFeedbackId(Long feedbackId);

    /**
     * 根据操作类型查询处理记录列表
     * 
     * @param actionType 操作类型
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByActionType(String actionType);

    /**
     * 根据操作人ID查询处理记录列表
     * 
     * @param operatorId 操作人ID
     * @return 反馈处理记录集合
     */
    public List<FeedbackProcessLog> selectFeedbackProcessLogByOperatorId(Long operatorId);

    /**
     * 新增反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    public int insertFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog);

    /**
     * 修改反馈处理记录
     * 
     * @param feedbackProcessLog 反馈处理记录
     * @return 结果
     */
    public int updateFeedbackProcessLog(FeedbackProcessLog feedbackProcessLog);

    /**
     * 批量删除反馈处理记录
     * 
     * @param ids 需要删除的反馈处理记录主键集合
     * @return 结果
     */
    public int deleteFeedbackProcessLogByIds(Long[] ids);

    /**
     * 删除反馈处理记录信息
     * 
     * @param id 反馈处理记录主键
     * @return 结果
     */
    public int deleteFeedbackProcessLogById(Long id);

    /**
     * 记录反馈创建操作
     * 
     * @param feedbackId 反馈ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackCreate(Long feedbackId, Long operatorId, String operatorName);

    /**
     * 记录反馈分配操作
     * 
     * @param feedbackId 反馈ID
     * @param oldHandlerId 原处理人ID
     * @param newHandlerId 新处理人ID
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackAssign(Long feedbackId, Long oldHandlerId, Long newHandlerId, Long operatorId, String operatorName);

    /**
     * 记录反馈处理操作
     * 
     * @param feedbackId 反馈ID
     * @param processContent 处理内容
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackProcess(Long feedbackId, String processContent, Long operatorId, String operatorName);

    /**
     * 记录反馈关闭操作
     * 
     * @param feedbackId 反馈ID
     * @param closeReason 关闭原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackClose(Long feedbackId, String closeReason, Long operatorId, String operatorName);

    /**
     * 记录反馈重开操作
     * 
     * @param feedbackId 反馈ID
     * @param reopenReason 重开原因
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackReopen(Long feedbackId, String reopenReason, Long operatorId, String operatorName);

    /**
     * 记录反馈状态变更操作
     * 
     * @param feedbackId 反馈ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param actionDescription 操作描述
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 结果
     */
    public int logFeedbackStatusChange(Long feedbackId, String oldStatus, String newStatus, String actionDescription, Long operatorId, String operatorName);
}
