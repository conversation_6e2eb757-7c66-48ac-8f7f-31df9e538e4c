package com.ruoyi.web.controller.feedback;

import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.feedback.domain.FeedbackStatistics;
import com.ruoyi.feedback.service.IFeedbackStatisticsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 反馈统计Controller
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@RestController
@RequestMapping("/feedback/statistics")
public class FeedbackStatisticsController extends BaseController
{
    @Autowired
    private IFeedbackStatisticsService feedbackStatisticsService;

    /**
     * 查询反馈统计列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(FeedbackStatistics feedbackStatistics)
    {
        startPage();
        List<FeedbackStatistics> list = feedbackStatisticsService.selectFeedbackStatisticsList(feedbackStatistics);
        return getDataTable(list);
    }

    /**
     * 根据日期范围查询统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:list')")
    @GetMapping("/range")
    public TableDataInfo getByDateRange(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                      @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                      @RequestParam("statType") String statType)
    {
        startPage();
        List<FeedbackStatistics> list = feedbackStatisticsService.selectFeedbackStatisticsByDateRange(startDate, endDate, statType);
        return getDataTable(list);
    }

    /**
     * 获取反馈概览数据（仪表板用）
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:overview')")
    @GetMapping("/overview")
    public AjaxResult getOverviewData()
    {
        Map<String, Object> data = feedbackStatisticsService.getFeedbackOverviewData();
        return success(data);
    }

    /**
     * 获取反馈趋势数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:trend')")
    @GetMapping("/trend")
    public AjaxResult getTrendData(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                 @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                 @RequestParam("statType") String statType)
    {
        List<Map<String, Object>> data = feedbackStatisticsService.getFeedbackTrendData(startDate, endDate, statType);
        return success(data);
    }

    /**
     * 获取反馈分类分布数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:distribution')")
    @GetMapping("/categoryDistribution")
    public AjaxResult getCategoryDistribution(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> data = feedbackStatisticsService.getFeedbackCategoryDistribution(startDate, endDate);
        return success(data);
    }

    /**
     * 获取反馈状态分布数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:distribution')")
    @GetMapping("/statusDistribution")
    public AjaxResult getStatusDistribution(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                          @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        List<Map<String, Object>> data = feedbackStatisticsService.getFeedbackStatusDistribution(startDate, endDate);
        return success(data);
    }

    /**
     * 获取反馈处理效率数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:efficiency')")
    @GetMapping("/efficiency")
    public AjaxResult getProcessingEfficiency(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> data = feedbackStatisticsService.getFeedbackProcessingEfficiency(startDate, endDate);
        return success(data);
    }

    /**
     * 获取反馈满意度数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:satisfaction')")
    @GetMapping("/satisfaction")
    public AjaxResult getSatisfactionData(@RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                                        @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate)
    {
        Map<String, Object> data = feedbackStatisticsService.getFeedbackSatisfactionData(startDate, endDate);
        return success(data);
    }

    /**
     * 导出反馈统计列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:export')")
    @Log(title = "反馈统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FeedbackStatistics feedbackStatistics)
    {
        List<FeedbackStatistics> list = feedbackStatisticsService.selectFeedbackStatisticsList(feedbackStatistics);
        ExcelUtil<FeedbackStatistics> util = new ExcelUtil<FeedbackStatistics>(FeedbackStatistics.class);
        util.exportExcel(response, list, "反馈统计数据");
    }

    /**
     * 获取反馈统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(feedbackStatisticsService.selectFeedbackStatisticsById(id));
    }

    /**
     * 新增反馈统计
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:add')")
    @Log(title = "反馈统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FeedbackStatistics feedbackStatistics)
    {
        return toAjax(feedbackStatisticsService.insertFeedbackStatistics(feedbackStatistics));
    }

    /**
     * 修改反馈统计
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:edit')")
    @Log(title = "反馈统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FeedbackStatistics feedbackStatistics)
    {
        return toAjax(feedbackStatisticsService.updateFeedbackStatistics(feedbackStatistics));
    }

    /**
     * 生成日统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:generate')")
    @Log(title = "生成日统计", businessType = BusinessType.INSERT)
    @PostMapping("/generateDaily")
    public AjaxResult generateDaily(@RequestParam("statDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date statDate)
    {
        return toAjax(feedbackStatisticsService.generateDailyStatistics(statDate));
    }

    /**
     * 生成周统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:generate')")
    @Log(title = "生成周统计", businessType = BusinessType.INSERT)
    @PostMapping("/generateWeekly")
    public AjaxResult generateWeekly(@RequestParam("statDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date statDate)
    {
        return toAjax(feedbackStatisticsService.generateWeeklyStatistics(statDate));
    }

    /**
     * 生成月统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:generate')")
    @Log(title = "生成月统计", businessType = BusinessType.INSERT)
    @PostMapping("/generateMonthly")
    public AjaxResult generateMonthly(@RequestParam("statDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date statDate)
    {
        return toAjax(feedbackStatisticsService.generateMonthlyStatistics(statDate));
    }

    /**
     * 自动生成统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:generate')")
    @Log(title = "自动生成统计", businessType = BusinessType.INSERT)
    @PostMapping("/autoGenerate")
    public AjaxResult autoGenerate()
    {
        return toAjax(feedbackStatisticsService.autoGenerateStatistics());
    }

    /**
     * 清理过期统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:clean')")
    @Log(title = "清理过期统计数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean")
    public AjaxResult cleanExpired(@RequestParam("beforeDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beforeDate)
    {
        return toAjax(feedbackStatisticsService.cleanExpiredStatistics(beforeDate));
    }

    /**
     * 删除反馈统计
     */
    @PreAuthorize("@ss.hasPermi('feedback:statistics:remove')")
    @Log(title = "反馈统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(feedbackStatisticsService.deleteFeedbackStatisticsByIds(ids));
    }
}
