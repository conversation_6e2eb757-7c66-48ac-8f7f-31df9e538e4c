package com.ruoyi.feedback.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.feedback.mapper.KnowledgeOptimizationSuggestionMapper;
import com.ruoyi.feedback.mapper.UserFeedbackMapper;
import com.ruoyi.feedback.domain.KnowledgeOptimizationSuggestion;
import com.ruoyi.feedback.domain.UserFeedback;
import com.ruoyi.feedback.service.IKnowledgeOptimizationSuggestionService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.alibaba.fastjson2.JSON;

/**
 * 知识库优化建议Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Service
public class KnowledgeOptimizationSuggestionServiceImpl implements IKnowledgeOptimizationSuggestionService 
{
    @Autowired
    private KnowledgeOptimizationSuggestionMapper knowledgeOptimizationSuggestionMapper;

    @Autowired
    private UserFeedbackMapper userFeedbackMapper;

    /**
     * 查询知识库优化建议
     * 
     * @param id 知识库优化建议主键
     * @return 知识库优化建议
     */
    @Override
    public KnowledgeOptimizationSuggestion selectKnowledgeOptimizationSuggestionById(Long id)
    {
        return knowledgeOptimizationSuggestionMapper.selectKnowledgeOptimizationSuggestionById(id);
    }

    /**
     * 查询知识库优化建议列表
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 知识库优化建议
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectKnowledgeOptimizationSuggestionList(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        return knowledgeOptimizationSuggestionMapper.selectKnowledgeOptimizationSuggestionList(knowledgeOptimizationSuggestion);
    }

    /**
     * 根据建议类型查询优化建议列表
     * 
     * @param suggestionType 建议类型
     * @return 知识库优化建议集合
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectSuggestionBySuggestionType(String suggestionType)
    {
        return knowledgeOptimizationSuggestionMapper.selectSuggestionBySuggestionType(suggestionType);
    }

    /**
     * 根据目标类型和目标ID查询优化建议列表
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 知识库优化建议集合
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByTarget(String targetType, Long targetId)
    {
        return knowledgeOptimizationSuggestionMapper.selectSuggestionByTarget(targetType, targetId);
    }

    /**
     * 查询自动生成的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectAutoGeneratedSuggestionList()
    {
        return knowledgeOptimizationSuggestionMapper.selectAutoGeneratedSuggestionList();
    }

    /**
     * 查询待审核的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectPendingReviewSuggestionList()
    {
        return knowledgeOptimizationSuggestionMapper.selectPendingReviewSuggestionList();
    }

    /**
     * 查询已通过待实施的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    @Override
    public List<KnowledgeOptimizationSuggestion> selectApprovedSuggestionList()
    {
        return knowledgeOptimizationSuggestionMapper.selectApprovedSuggestionList();
    }

    /**
     * 统计建议数量按类型
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectSuggestionCountByType()
    {
        return knowledgeOptimizationSuggestionMapper.selectSuggestionCountByType();
    }

    /**
     * 统计建议数量按状态
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectSuggestionCountByStatus()
    {
        return knowledgeOptimizationSuggestionMapper.selectSuggestionCountByStatus();
    }

    /**
     * 新增知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    @Override
    public int insertKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        knowledgeOptimizationSuggestion.setCreateTime(DateUtils.getNowDate());
        return knowledgeOptimizationSuggestionMapper.insertKnowledgeOptimizationSuggestion(knowledgeOptimizationSuggestion);
    }

    /**
     * 修改知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    @Override
    public int updateKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion)
    {
        knowledgeOptimizationSuggestion.setUpdateTime(DateUtils.getNowDate());
        return knowledgeOptimizationSuggestionMapper.updateKnowledgeOptimizationSuggestion(knowledgeOptimizationSuggestion);
    }

    /**
     * 审核优化建议
     * 
     * @param suggestionId 建议ID
     * @param status 审核状态（1通过 2拒绝）
     * @param reviewComment 审核意见
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int reviewSuggestion(Long suggestionId, String status, String reviewComment, Long reviewerId, String reviewerName)
    {
        KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
        suggestion.setId(suggestionId);
        suggestion.setStatus(status);
        suggestion.setReviewComment(reviewComment);
        suggestion.setReviewerId(reviewerId);
        suggestion.setReviewerName(reviewerName);
        suggestion.setReviewTime(DateUtils.getNowDate());
        suggestion.setUpdateTime(DateUtils.getNowDate());
        
        return knowledgeOptimizationSuggestionMapper.updateKnowledgeOptimizationSuggestion(suggestion);
    }

    /**
     * 批量审核优化建议
     * 
     * @param suggestionIds 建议ID数组
     * @param status 审核状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int batchReviewSuggestion(Long[] suggestionIds, String status, Long reviewerId, String reviewerName)
    {
        return knowledgeOptimizationSuggestionMapper.batchUpdateSuggestionStatus(suggestionIds, status, reviewerId, reviewerName);
    }

    /**
     * 实施优化建议
     * 
     * @param suggestionId 建议ID
     * @param implementResult 实施结果
     * @param implementerId 实施人ID
     * @param implementerName 实施人姓名
     * @return 结果
     */
    @Override
    @Transactional
    public int implementSuggestion(Long suggestionId, String implementResult, Long implementerId, String implementerName)
    {
        KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
        suggestion.setId(suggestionId);
        suggestion.setStatus("3"); // 已实施
        suggestion.setImplementResult(implementResult);
        suggestion.setImplementerId(implementerId);
        suggestion.setImplementerName(implementerName);
        suggestion.setImplementTime(DateUtils.getNowDate());
        suggestion.setUpdateTime(DateUtils.getNowDate());
        
        return knowledgeOptimizationSuggestionMapper.updateKnowledgeOptimizationSuggestion(suggestion);
    }

    /**
     * 关闭优化建议
     * 
     * @param suggestionId 建议ID
     * @param closeReason 关闭原因
     * @return 结果
     */
    @Override
    @Transactional
    public int closeSuggestion(Long suggestionId, String closeReason)
    {
        KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
        suggestion.setId(suggestionId);
        suggestion.setStatus("4"); // 已关闭
        suggestion.setReviewComment(closeReason);
        suggestion.setUpdateTime(DateUtils.getNowDate());
        
        return knowledgeOptimizationSuggestionMapper.updateKnowledgeOptimizationSuggestion(suggestion);
    }

    /**
     * 批量删除知识库优化建议
     * 
     * @param ids 需要删除的知识库优化建议主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeOptimizationSuggestionByIds(Long[] ids)
    {
        return knowledgeOptimizationSuggestionMapper.deleteKnowledgeOptimizationSuggestionByIds(ids);
    }

    /**
     * 删除知识库优化建议信息
     * 
     * @param id 知识库优化建议主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeOptimizationSuggestionById(Long id)
    {
        return knowledgeOptimizationSuggestionMapper.deleteKnowledgeOptimizationSuggestionById(id);
    }

    /**
     * 基于反馈数据生成优化建议
     * 
     * @param feedbackIds 反馈ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int generateSuggestionFromFeedback(List<Long> feedbackIds)
    {
        if (feedbackIds == null || feedbackIds.isEmpty()) {
            return 0;
        }

        // 分析反馈数据，生成优化建议
        // 这里是简化的实现，实际应该使用AI算法分析
        for (Long feedbackId : feedbackIds) {
            UserFeedback feedback = userFeedbackMapper.selectUserFeedbackById(feedbackId);
            if (feedback != null) {
                KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
                suggestion.setTitle("基于反馈的优化建议 - " + feedback.getTitle());
                suggestion.setDescription("根据用户反馈分析生成的优化建议");
                suggestion.setSuggestionType("content"); // 默认为内容优化
                suggestion.setTargetType("knowledge_base");
                suggestion.setTargetId(feedback.getKnowledgeBaseId());
                suggestion.setTargetName(feedback.getKnowledgeBaseName());
                suggestion.setOptimizationReason(feedback.getContent());
                suggestion.setPriority("2"); // 中等优先级
                suggestion.setConfidenceScore(new BigDecimal("0.75")); // 默认置信度
                suggestion.setSourceFeedbackIds(JSON.toJSONString(feedbackIds));
                suggestion.setFeedbackCount(1);
                suggestion.setAutoGenerated("1");
                suggestion.setAlgorithmVersion("1.0");
                suggestion.setStatus("0"); // 待审核
                suggestion.setCreateTime(DateUtils.getNowDate());
                
                knowledgeOptimizationSuggestionMapper.insertKnowledgeOptimizationSuggestion(suggestion);
            }
        }
        
        return feedbackIds.size();
    }

    /**
     * 自动生成知识库优化建议
     * 
     * @param knowledgeBaseId 知识库ID（可为空，表示全部知识库）
     * @return 结果
     */
    @Override
    @Transactional
    public int autoGenerateOptimizationSuggestions(Long knowledgeBaseId)
    {
        // 这里是简化的实现，实际应该使用AI算法分析知识库内容和用户反馈
        // 生成针对性的优化建议
        
        // 查询相关反馈数据
        UserFeedback queryFeedback = new UserFeedback();
        if (knowledgeBaseId != null) {
            queryFeedback.setKnowledgeBaseId(knowledgeBaseId);
        }
        List<UserFeedback> feedbackList = userFeedbackMapper.selectUserFeedbackList(queryFeedback);
        
        int count = 0;
        // 基于反馈数据生成建议
        for (UserFeedback feedback : feedbackList) {
            // 简化的建议生成逻辑
            if (StringUtils.isNotEmpty(feedback.getContent()) && feedback.getRating() != null && feedback.getRating() < 3) {
                KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
                suggestion.setTitle("自动生成优化建议 - " + feedback.getCategoryName());
                suggestion.setDescription("基于低评分反馈自动生成的优化建议");
                suggestion.setSuggestionType("content");
                suggestion.setTargetType("knowledge_base");
                suggestion.setTargetId(feedback.getKnowledgeBaseId());
                suggestion.setTargetName(feedback.getKnowledgeBaseName());
                suggestion.setOptimizationReason("用户反馈评分较低，需要优化相关内容");
                suggestion.setExpectedEffect("提升用户满意度和内容质量");
                suggestion.setPriority("2");
                suggestion.setConfidenceScore(new BigDecimal("0.80"));
                suggestion.setSourceFeedbackIds("[" + feedback.getId() + "]");
                suggestion.setFeedbackCount(1);
                suggestion.setAutoGenerated("1");
                suggestion.setAlgorithmVersion("1.0");
                suggestion.setStatus("0");
                suggestion.setCreateTime(DateUtils.getNowDate());
                
                knowledgeOptimizationSuggestionMapper.insertKnowledgeOptimizationSuggestion(suggestion);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 评估优化建议的效果
     * 
     * @param suggestionId 建议ID
     * @param effectEvaluation 效果评估
     * @return 结果
     */
    @Override
    @Transactional
    public int evaluateSuggestionEffect(Long suggestionId, String effectEvaluation)
    {
        KnowledgeOptimizationSuggestion suggestion = new KnowledgeOptimizationSuggestion();
        suggestion.setId(suggestionId);
        suggestion.setEffectEvaluation(effectEvaluation);
        suggestion.setUpdateTime(DateUtils.getNowDate());
        
        return knowledgeOptimizationSuggestionMapper.updateKnowledgeOptimizationSuggestion(suggestion);
    }
}
