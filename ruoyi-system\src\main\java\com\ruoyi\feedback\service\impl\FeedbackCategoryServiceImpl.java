package com.ruoyi.feedback.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.feedback.mapper.FeedbackCategoryMapper;
import com.ruoyi.feedback.domain.FeedbackCategory;
import com.ruoyi.feedback.service.IFeedbackCategoryService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;

/**
 * 反馈分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Service
public class FeedbackCategoryServiceImpl implements IFeedbackCategoryService 
{
    @Autowired
    private FeedbackCategoryMapper feedbackCategoryMapper;

    /**
     * 查询反馈分类
     * 
     * @param id 反馈分类主键
     * @return 反馈分类
     */
    @Override
    public FeedbackCategory selectFeedbackCategoryById(Long id)
    {
        return feedbackCategoryMapper.selectFeedbackCategoryById(id);
    }

    /**
     * 根据分类编码查询反馈分类
     * 
     * @param code 分类编码
     * @return 反馈分类
     */
    @Override
    public FeedbackCategory selectFeedbackCategoryByCode(String code)
    {
        return feedbackCategoryMapper.selectFeedbackCategoryByCode(code);
    }

    /**
     * 查询反馈分类列表
     * 
     * @param feedbackCategory 反馈分类
     * @return 反馈分类
     */
    @Override
    public List<FeedbackCategory> selectFeedbackCategoryList(FeedbackCategory feedbackCategory)
    {
        return feedbackCategoryMapper.selectFeedbackCategoryList(feedbackCategory);
    }

    /**
     * 查询启用的反馈分类列表
     * 
     * @return 反馈分类集合
     */
    @Override
    public List<FeedbackCategory> selectEnabledFeedbackCategoryList()
    {
        return feedbackCategoryMapper.selectEnabledFeedbackCategoryList();
    }

    /**
     * 新增反馈分类
     * 
     * @param feedbackCategory 反馈分类
     * @return 结果
     */
    @Override
    public int insertFeedbackCategory(FeedbackCategory feedbackCategory)
    {
        feedbackCategory.setCreateTime(DateUtils.getNowDate());
        return feedbackCategoryMapper.insertFeedbackCategory(feedbackCategory);
    }

    /**
     * 修改反馈分类
     * 
     * @param feedbackCategory 反馈分类
     * @return 结果
     */
    @Override
    public int updateFeedbackCategory(FeedbackCategory feedbackCategory)
    {
        feedbackCategory.setUpdateTime(DateUtils.getNowDate());
        return feedbackCategoryMapper.updateFeedbackCategory(feedbackCategory);
    }

    /**
     * 批量删除反馈分类
     * 
     * @param ids 需要删除的反馈分类主键
     * @return 结果
     */
    @Override
    public int deleteFeedbackCategoryByIds(Long[] ids)
    {
        return feedbackCategoryMapper.deleteFeedbackCategoryByIds(ids);
    }

    /**
     * 删除反馈分类信息
     * 
     * @param id 反馈分类主键
     * @return 结果
     */
    @Override
    public int deleteFeedbackCategoryById(Long id)
    {
        return feedbackCategoryMapper.deleteFeedbackCategoryById(id);
    }

    /**
     * 检查分类编码是否唯一
     * 
     * @param feedbackCategory 反馈分类信息
     * @return 结果
     */
    @Override
    public boolean checkCodeUnique(FeedbackCategory feedbackCategory)
    {
        Long categoryId = StringUtils.isNull(feedbackCategory.getId()) ? -1L : feedbackCategory.getId();
        FeedbackCategory info = feedbackCategoryMapper.checkCodeUnique(feedbackCategory);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != categoryId.longValue())
        {
            return false;
        }
        return true;
    }
}
