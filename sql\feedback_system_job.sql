-- 反馈系统定时任务配置
-- 创建时间：2025-08-29

-- 获取下一个任务ID
SET @job_id = (SELECT IFNULL(MAX(job_id), 0) + 1 FROM sys_job);

-- 1. 反馈统计数据自动生成任务
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '反馈统计数据生成', 'FEEDBACK', 'feedbackStatisticsTask.autoGenerateStatistics', '0 0 1 * * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每天凌晨1点自动生成反馈统计数据');

-- 2. 知识库优化建议自动生成任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '优化建议自动生成', 'FEEDBACK', 'knowledgeOptimizationTask.autoGenerateOptimizationSuggestions', '0 0 2 * * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每天凌晨2点自动生成知识库优化建议');

-- 3. 过期统计数据清理任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '过期统计数据清理', 'FEEDBACK', 'feedbackStatisticsTask.cleanExpiredStatistics', '0 0 3 1 * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每月1号凌晨3点清理过期统计数据（保留1年）');

-- 4. 反馈处理超时提醒任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '反馈处理超时提醒', 'FEEDBACK', 'feedbackReminderTask.checkOverdueFeedback', '0 0 9,14,18 * * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每天9点、14点、18点检查超时未处理的反馈并发送提醒');

-- 5. 反馈满意度分析任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '反馈满意度分析', 'FEEDBACK', 'feedbackAnalysisTask.analyzeSatisfaction', '0 30 0 * * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每天凌晨0点30分分析反馈满意度趋势');

-- 6. 知识库内容质量评估任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '知识库内容质量评估', 'FEEDBACK', 'knowledgeQualityTask.evaluateContentQuality', '0 0 4 * * 1', '3', '1', '0', 'admin', sysdate(), '', NULL, '每周一凌晨4点基于反馈数据评估知识库内容质量');

-- 7. 反馈数据备份任务
SET @job_id = @job_id + 1;
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark)
VALUES (@job_id, '反馈数据备份', 'FEEDBACK', 'feedbackBackupTask.backupFeedbackData', '0 0 5 1 * ?', '3', '1', '0', 'admin', sysdate(), '', NULL, '每月1号凌晨5点备份反馈相关数据');
