<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.feedback.mapper.KnowledgeOptimizationSuggestionMapper">
    
    <resultMap type="KnowledgeOptimizationSuggestion" id="KnowledgeOptimizationSuggestionResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="suggestionType"    column="suggestion_type"    />
        <result property="targetType"    column="target_type"    />
        <result property="targetId"    column="target_id"    />
        <result property="targetName"    column="target_name"    />
        <result property="currentContent"    column="current_content"    />
        <result property="suggestedContent"    column="suggested_content"    />
        <result property="optimizationReason"    column="optimization_reason"    />
        <result property="expectedEffect"    column="expected_effect"    />
        <result property="priority"    column="priority"    />
        <result property="confidenceScore"    column="confidence_score"    />
        <result property="sourceFeedbackIds"    column="source_feedback_ids"    />
        <result property="feedbackCount"    column="feedback_count"    />
        <result property="autoGenerated"    column="auto_generated"    />
        <result property="algorithmVersion"    column="algorithm_version"    />
        <result property="status"    column="status"    />
        <result property="reviewerId"    column="reviewer_id"    />
        <result property="reviewerName"    column="reviewer_name"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="reviewComment"    column="review_comment"    />
        <result property="implementerId"    column="implementer_id"    />
        <result property="implementerName"    column="implementer_name"    />
        <result property="implementTime"    column="implement_time"    />
        <result property="implementResult"    column="implement_result"    />
        <result property="effectEvaluation"    column="effect_evaluation"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKnowledgeOptimizationSuggestionVo">
        select id, title, description, suggestion_type, target_type, target_id, target_name, current_content, suggested_content, optimization_reason, expected_effect, priority, confidence_score, source_feedback_ids, feedback_count, auto_generated, algorithm_version, status, reviewer_id, reviewer_name, review_time, review_comment, implementer_id, implementer_name, implement_time, implement_result, effect_evaluation, create_by, create_time, update_by, update_time, remark from knowledge_optimization_suggestion
    </sql>

    <select id="selectKnowledgeOptimizationSuggestionList" parameterType="KnowledgeOptimizationSuggestion" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="suggestionType != null  and suggestionType != ''"> and suggestion_type = #{suggestionType}</if>
            <if test="targetType != null  and targetType != ''"> and target_type = #{targetType}</if>
            <if test="targetId != null "> and target_id = #{targetId}</if>
            <if test="priority != null  and priority != ''"> and priority = #{priority}</if>
            <if test="autoGenerated != null  and autoGenerated != ''"> and auto_generated = #{autoGenerated}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="reviewerId != null "> and reviewer_id = #{reviewerId}</if>
            <if test="implementerId != null "> and implementer_id = #{implementerId}</if>
        </where>
        order by priority asc, create_time desc
    </select>
    
    <select id="selectKnowledgeOptimizationSuggestionById" parameterType="Long" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where id = #{id}
    </select>

    <select id="selectSuggestionBySuggestionType" parameterType="String" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where suggestion_type = #{suggestionType}
        order by create_time desc
    </select>

    <select id="selectSuggestionByTarget" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where target_type = #{targetType} and target_id = #{targetId}
        order by create_time desc
    </select>

    <select id="selectSuggestionByStatus" parameterType="String" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where status = #{status}
        order by priority asc, create_time desc
    </select>

    <select id="selectSuggestionByReviewerId" parameterType="Long" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where reviewer_id = #{reviewerId}
        order by review_time desc
    </select>

    <select id="selectSuggestionByImplementerId" parameterType="Long" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where implementer_id = #{implementerId}
        order by implement_time desc
    </select>

    <select id="selectAutoGeneratedSuggestionList" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where auto_generated = '1'
        order by confidence_score desc, create_time desc
    </select>

    <select id="selectPendingReviewSuggestionList" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where status = '0'
        order by priority asc, create_time asc
    </select>

    <select id="selectApprovedSuggestionList" resultMap="KnowledgeOptimizationSuggestionResult">
        <include refid="selectKnowledgeOptimizationSuggestionVo"/>
        where status = '1'
        order by priority asc, review_time desc
    </select>

    <select id="selectSuggestionCountByType" resultType="map">
        select suggestion_type, count(*) as count
        from knowledge_optimization_suggestion
        where status != '4'
        group by suggestion_type
        order by count desc
    </select>

    <select id="selectSuggestionCountByStatus" resultType="map">
        select status, count(*) as count
        from knowledge_optimization_suggestion
        group by status
        order by status
    </select>
        
    <insert id="insertKnowledgeOptimizationSuggestion" parameterType="KnowledgeOptimizationSuggestion" useGeneratedKeys="true" keyProperty="id">
        insert into knowledge_optimization_suggestion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="suggestionType != null">suggestion_type,</if>
            <if test="targetType != null">target_type,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetName != null">target_name,</if>
            <if test="currentContent != null">current_content,</if>
            <if test="suggestedContent != null">suggested_content,</if>
            <if test="optimizationReason != null">optimization_reason,</if>
            <if test="expectedEffect != null">expected_effect,</if>
            <if test="priority != null">priority,</if>
            <if test="confidenceScore != null">confidence_score,</if>
            <if test="sourceFeedbackIds != null">source_feedback_ids,</if>
            <if test="feedbackCount != null">feedback_count,</if>
            <if test="autoGenerated != null">auto_generated,</if>
            <if test="algorithmVersion != null">algorithm_version,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="suggestionType != null">#{suggestionType},</if>
            <if test="targetType != null">#{targetType},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetName != null">#{targetName},</if>
            <if test="currentContent != null">#{currentContent},</if>
            <if test="suggestedContent != null">#{suggestedContent},</if>
            <if test="optimizationReason != null">#{optimizationReason},</if>
            <if test="expectedEffect != null">#{expectedEffect},</if>
            <if test="priority != null">#{priority},</if>
            <if test="confidenceScore != null">#{confidenceScore},</if>
            <if test="sourceFeedbackIds != null">#{sourceFeedbackIds},</if>
            <if test="feedbackCount != null">#{feedbackCount},</if>
            <if test="autoGenerated != null">#{autoGenerated},</if>
            <if test="algorithmVersion != null">#{algorithmVersion},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateKnowledgeOptimizationSuggestion" parameterType="KnowledgeOptimizationSuggestion">
        update knowledge_optimization_suggestion
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="suggestionType != null">suggestion_type = #{suggestionType},</if>
            <if test="currentContent != null">current_content = #{currentContent},</if>
            <if test="suggestedContent != null">suggested_content = #{suggestedContent},</if>
            <if test="optimizationReason != null">optimization_reason = #{optimizationReason},</if>
            <if test="expectedEffect != null">expected_effect = #{expectedEffect},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
            <if test="reviewerName != null">reviewer_name = #{reviewerName},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="implementerId != null">implementer_id = #{implementerId},</if>
            <if test="implementerName != null">implementer_name = #{implementerName},</if>
            <if test="implementTime != null">implement_time = #{implementTime},</if>
            <if test="implementResult != null">implement_result = #{implementResult},</if>
            <if test="effectEvaluation != null">effect_evaluation = #{effectEvaluation},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateSuggestionStatus">
        update knowledge_optimization_suggestion set 
        status = #{status},
        <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
        <if test="reviewerName != null">reviewer_name = #{reviewerName},</if>
        review_time = sysdate(),
        update_time = sysdate()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteKnowledgeOptimizationSuggestionById" parameterType="Long">
        delete from knowledge_optimization_suggestion where id = #{id}
    </delete>

    <delete id="deleteKnowledgeOptimizationSuggestionByIds" parameterType="String">
        delete from knowledge_optimization_suggestion where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
