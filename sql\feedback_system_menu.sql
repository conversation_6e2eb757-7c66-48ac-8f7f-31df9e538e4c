-- 反馈系统菜单和权限配置
-- 创建时间：2025-08-29

-- 获取下一个菜单ID
SET @menu_id = (SELECT IFNULL(MAX(menu_id), 0) + 1 FROM sys_menu);

-- 1. 反馈管理主菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈管理', 0, 6, 'feedback', NULL, '', 1, 0, 'M', '0', '0', '', 'message', 'admin', sysdate(), '', NULL, '反馈管理目录');

-- 2. 反馈分类管理
SET @parent_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类', @parent_menu_id, 1, 'category', 'feedback/category/index', '', 1, 0, 'C', '0', '0', 'feedback:category:list', 'tree-table', 'admin', sysdate(), '', NULL, '反馈分类菜单');

-- 反馈分类查询
SET @category_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类查询', @category_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'feedback:category:query', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈分类新增
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类新增', @category_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'feedback:category:add', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈分类修改
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类修改', @category_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'feedback:category:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈分类删除
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类删除', @category_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'feedback:category:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈分类导出
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分类导出', @category_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'feedback:category:export', '#', 'admin', sysdate(), '', NULL, '');

-- 3. 用户反馈管理
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈', @parent_menu_id, 2, 'user', 'feedback/user/index', '', 1, 0, 'C', '0', '0', 'feedback:user:list', 'edit', 'admin', sysdate(), '', NULL, '用户反馈菜单');

-- 用户反馈查询
SET @user_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈查询', @user_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:query', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈新增
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈新增', @user_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:add', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈修改
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈修改', @user_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈删除
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈删除', @user_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈导出
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈导出', @user_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:export', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈分配
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈分配', @user_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:assign', '#', 'admin', sysdate(), '', NULL, '');

-- 用户反馈处理
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '用户反馈处理', @user_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'feedback:user:handle', '#', 'admin', sysdate(), '', NULL, '');

-- 4. 优化建议管理
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议', @parent_menu_id, 3, 'suggestion', 'feedback/suggestion/index', '', 1, 0, 'C', '0', '0', 'feedback:suggestion:list', 'guide', 'admin', sysdate(), '', NULL, '优化建议菜单');

-- 优化建议查询
SET @suggestion_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议查询', @suggestion_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:query', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议新增
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议新增', @suggestion_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:add', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议修改
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议修改', @suggestion_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:edit', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议删除
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议删除', @suggestion_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:remove', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议导出
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议导出', @suggestion_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:export', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议审核
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议审核', @suggestion_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:review', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议实施
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议实施', @suggestion_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:implement', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议生成
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议生成', @suggestion_menu_id, 8, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:generate', '#', 'admin', sysdate(), '', NULL, '');

-- 优化建议评估
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '优化建议评估', @suggestion_menu_id, 9, '', '', '', 1, 0, 'F', '0', '0', 'feedback:suggestion:evaluate', '#', 'admin', sysdate(), '', NULL, '');

-- 5. 反馈统计
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈统计', @parent_menu_id, 4, 'statistics', 'feedback/statistics/index', '', 1, 0, 'C', '0', '0', 'feedback:statistics:overview', 'chart', 'admin', sysdate(), '', NULL, '反馈统计菜单');

-- 反馈统计查询
SET @statistics_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈统计查询', @statistics_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:list', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈趋势分析
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈趋势分析', @statistics_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:trend', '#', 'admin', sysdate(), '', NULL, '');

-- 反馈分布统计
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '反馈分布统计', @statistics_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:distribution', '#', 'admin', sysdate(), '', NULL, '');

-- 处理效率统计
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '处理效率统计', @statistics_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:efficiency', '#', 'admin', sysdate(), '', NULL, '');

-- 满意度统计
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '满意度统计', @statistics_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:satisfaction', '#', 'admin', sysdate(), '', NULL, '');

-- 统计数据生成
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '统计数据生成', @statistics_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:generate', '#', 'admin', sysdate(), '', NULL, '');

-- 统计数据清理
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '统计数据清理', @statistics_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'feedback:statistics:clean', '#', 'admin', sysdate(), '', NULL, '');

-- 6. 处理记录查询
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '处理记录', @parent_menu_id, 5, 'processLog', 'feedback/processLog/index', '', 1, 0, 'C', '0', '0', 'feedback:processLog:list', 'log', 'admin', sysdate(), '', NULL, '处理记录菜单');

-- 处理记录查询
SET @log_menu_id = @menu_id;
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '处理记录查询', @log_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'feedback:processLog:query', '#', 'admin', sysdate(), '', NULL, '');

-- 处理记录导出
SET @menu_id = @menu_id + 1;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (@menu_id, '处理记录导出', @log_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'feedback:processLog:export', '#', 'admin', sysdate(), '', NULL, '');
