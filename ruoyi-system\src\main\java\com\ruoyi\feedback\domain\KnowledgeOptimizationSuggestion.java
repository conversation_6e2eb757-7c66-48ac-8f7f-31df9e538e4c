package com.ruoyi.feedback.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 知识库优化建议对象 knowledge_optimization_suggestion
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class KnowledgeOptimizationSuggestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 建议ID */
    private Long id;

    /** 建议标题 */
    @Excel(name = "建议标题")
    private String title;

    /** 建议描述 */
    @Excel(name = "建议描述")
    private String description;

    /** 建议类型（content内容优化 structure结构调整 tag标签完善 search检索优化） */
    @Excel(name = "建议类型", readConverterExp = "content=内容优化,structure=结构调整,tag=标签完善,search=检索优化")
    private String suggestionType;

    /** 目标类型（knowledge_base知识库 document文档 category分类） */
    @Excel(name = "目标类型", readConverterExp = "knowledge_base=知识库,document=文档,category=分类")
    private String targetType;

    /** 目标ID */
    @Excel(name = "目标ID")
    private Long targetId;

    /** 目标名称（冗余字段） */
    @Excel(name = "目标名称")
    private String targetName;

    /** 当前内容 */
    @Excel(name = "当前内容")
    private String currentContent;

    /** 建议内容 */
    @Excel(name = "建议内容")
    private String suggestedContent;

    /** 优化原因 */
    @Excel(name = "优化原因")
    private String optimizationReason;

    /** 预期效果 */
    @Excel(name = "预期效果")
    private String expectedEffect;

    /** 优先级（1高 2中 3低） */
    @Excel(name = "优先级", readConverterExp = "1=高,2=中,3=低")
    private String priority;

    /** 置信度评分（0.00-1.00） */
    @Excel(name = "置信度评分")
    private BigDecimal confidenceScore;

    /** 来源反馈ID列表（JSON格式） */
    @Excel(name = "来源反馈ID列表")
    private String sourceFeedbackIds;

    /** 相关反馈数量 */
    @Excel(name = "相关反馈数量")
    private Integer feedbackCount;

    /** 是否自动生成（0否 1是） */
    @Excel(name = "是否自动生成", readConverterExp = "0=否,1=是")
    private String autoGenerated;

    /** 算法版本 */
    @Excel(name = "算法版本")
    private String algorithmVersion;

    /** 状态（0待审核 1已通过 2已拒绝 3已实施 4已关闭） */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝,3=已实施,4=已关闭")
    private String status;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long reviewerId;

    /** 审核人姓名 */
    @Excel(name = "审核人姓名")
    private String reviewerName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 实施人ID */
    @Excel(name = "实施人ID")
    private Long implementerId;

    /** 实施人姓名 */
    @Excel(name = "实施人姓名")
    private String implementerName;

    /** 实施时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实施时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date implementTime;

    /** 实施结果 */
    @Excel(name = "实施结果")
    private String implementResult;

    /** 效果评估 */
    @Excel(name = "效果评估")
    private String effectEvaluation;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setSuggestionType(String suggestionType) 
    {
        this.suggestionType = suggestionType;
    }

    public String getSuggestionType() 
    {
        return suggestionType;
    }

    public void setTargetType(String targetType) 
    {
        this.targetType = targetType;
    }

    public String getTargetType() 
    {
        return targetType;
    }

    public void setTargetId(Long targetId) 
    {
        this.targetId = targetId;
    }

    public Long getTargetId() 
    {
        return targetId;
    }

    public void setTargetName(String targetName) 
    {
        this.targetName = targetName;
    }

    public String getTargetName() 
    {
        return targetName;
    }

    public void setCurrentContent(String currentContent) 
    {
        this.currentContent = currentContent;
    }

    public String getCurrentContent() 
    {
        return currentContent;
    }

    public void setSuggestedContent(String suggestedContent) 
    {
        this.suggestedContent = suggestedContent;
    }

    public String getSuggestedContent() 
    {
        return suggestedContent;
    }

    public void setOptimizationReason(String optimizationReason) 
    {
        this.optimizationReason = optimizationReason;
    }

    public String getOptimizationReason() 
    {
        return optimizationReason;
    }

    public void setExpectedEffect(String expectedEffect) 
    {
        this.expectedEffect = expectedEffect;
    }

    public String getExpectedEffect() 
    {
        return expectedEffect;
    }

    public void setPriority(String priority) 
    {
        this.priority = priority;
    }

    public String getPriority() 
    {
        return priority;
    }

    public void setConfidenceScore(BigDecimal confidenceScore) 
    {
        this.confidenceScore = confidenceScore;
    }

    public BigDecimal getConfidenceScore() 
    {
        return confidenceScore;
    }

    public void setSourceFeedbackIds(String sourceFeedbackIds) 
    {
        this.sourceFeedbackIds = sourceFeedbackIds;
    }

    public String getSourceFeedbackIds() 
    {
        return sourceFeedbackIds;
    }

    public void setFeedbackCount(Integer feedbackCount) 
    {
        this.feedbackCount = feedbackCount;
    }

    public Integer getFeedbackCount() 
    {
        return feedbackCount;
    }

    public void setAutoGenerated(String autoGenerated) 
    {
        this.autoGenerated = autoGenerated;
    }

    public String getAutoGenerated() 
    {
        return autoGenerated;
    }

    public void setAlgorithmVersion(String algorithmVersion) 
    {
        this.algorithmVersion = algorithmVersion;
    }

    public String getAlgorithmVersion() 
    {
        return algorithmVersion;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setReviewerId(Long reviewerId) 
    {
        this.reviewerId = reviewerId;
    }

    public Long getReviewerId() 
    {
        return reviewerId;
    }

    public void setReviewerName(String reviewerName) 
    {
        this.reviewerName = reviewerName;
    }

    public String getReviewerName() 
    {
        return reviewerName;
    }

    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }

    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }

    public void setImplementerId(Long implementerId) 
    {
        this.implementerId = implementerId;
    }

    public Long getImplementerId() 
    {
        return implementerId;
    }

    public void setImplementerName(String implementerName) 
    {
        this.implementerName = implementerName;
    }

    public String getImplementerName() 
    {
        return implementerName;
    }

    public void setImplementTime(Date implementTime) 
    {
        this.implementTime = implementTime;
    }

    public Date getImplementTime() 
    {
        return implementTime;
    }

    public void setImplementResult(String implementResult) 
    {
        this.implementResult = implementResult;
    }

    public String getImplementResult() 
    {
        return implementResult;
    }

    public void setEffectEvaluation(String effectEvaluation) 
    {
        this.effectEvaluation = effectEvaluation;
    }

    public String getEffectEvaluation() 
    {
        return effectEvaluation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("description", getDescription())
            .append("suggestionType", getSuggestionType())
            .append("targetType", getTargetType())
            .append("targetId", getTargetId())
            .append("targetName", getTargetName())
            .append("currentContent", getCurrentContent())
            .append("suggestedContent", getSuggestedContent())
            .append("optimizationReason", getOptimizationReason())
            .append("expectedEffect", getExpectedEffect())
            .append("priority", getPriority())
            .append("confidenceScore", getConfidenceScore())
            .append("sourceFeedbackIds", getSourceFeedbackIds())
            .append("feedbackCount", getFeedbackCount())
            .append("autoGenerated", getAutoGenerated())
            .append("algorithmVersion", getAlgorithmVersion())
            .append("status", getStatus())
            .append("reviewerId", getReviewerId())
            .append("reviewerName", getReviewerName())
            .append("reviewTime", getReviewTime())
            .append("reviewComment", getReviewComment())
            .append("implementerId", getImplementerId())
            .append("implementerName", getImplementerName())
            .append("implementTime", getImplementTime())
            .append("implementResult", getImplementResult())
            .append("effectEvaluation", getEffectEvaluation())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
