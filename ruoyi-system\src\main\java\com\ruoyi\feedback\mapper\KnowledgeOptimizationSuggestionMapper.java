package com.ruoyi.feedback.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.feedback.domain.KnowledgeOptimizationSuggestion;

/**
 * 知识库优化建议Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface KnowledgeOptimizationSuggestionMapper 
{
    /**
     * 查询知识库优化建议
     * 
     * @param id 知识库优化建议主键
     * @return 知识库优化建议
     */
    public KnowledgeOptimizationSuggestion selectKnowledgeOptimizationSuggestionById(Long id);

    /**
     * 查询知识库优化建议列表
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectKnowledgeOptimizationSuggestionList(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 根据建议类型查询优化建议列表
     * 
     * @param suggestionType 建议类型
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionBySuggestionType(String suggestionType);

    /**
     * 根据目标类型和目标ID查询优化建议列表
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByTarget(String targetType, Long targetId);

    /**
     * 根据状态查询优化建议列表
     * 
     * @param status 状态
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByStatus(String status);

    /**
     * 根据审核人ID查询优化建议列表
     * 
     * @param reviewerId 审核人ID
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByReviewerId(Long reviewerId);

    /**
     * 根据实施人ID查询优化建议列表
     * 
     * @param implementerId 实施人ID
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByImplementerId(Long implementerId);

    /**
     * 查询自动生成的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectAutoGeneratedSuggestionList();

    /**
     * 查询待审核的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectPendingReviewSuggestionList();

    /**
     * 查询已通过待实施的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectApprovedSuggestionList();

    /**
     * 统计建议数量按类型
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> selectSuggestionCountByType();

    /**
     * 统计建议数量按状态
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> selectSuggestionCountByStatus();

    /**
     * 新增知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    public int insertKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 修改知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    public int updateKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 批量更新建议状态
     * 
     * @param ids 建议ID数组
     * @param status 新状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 结果
     */
    public int batchUpdateSuggestionStatus(Long[] ids, String status, Long reviewerId, String reviewerName);

    /**
     * 删除知识库优化建议
     * 
     * @param id 知识库优化建议主键
     * @return 结果
     */
    public int deleteKnowledgeOptimizationSuggestionById(Long id);

    /**
     * 批量删除知识库优化建议
     * 
     * @param ids 需要删除的知识库优化建议主键集合
     * @return 结果
     */
    public int deleteKnowledgeOptimizationSuggestionByIds(Long[] ids);
}
