import request from '@/utils/request'

// 查询反馈统计列表
export function listStatistics(query) {
  return request({
    url: '/feedback/statistics/list',
    method: 'get',
    params: query
  })
}

// 根据日期范围查询统计数据
export function getStatisticsByDateRange(startDate, endDate, statType) {
  return request({
    url: '/feedback/statistics/range',
    method: 'get',
    params: {
      startDate: startDate,
      endDate: endDate,
      statType: statType
    }
  })
}

// 获取反馈概览数据（仪表板用）
export function getOverviewData() {
  return request({
    url: '/feedback/statistics/overview',
    method: 'get'
  })
}

// 获取反馈趋势数据
export function getTrendData(params) {
  return request({
    url: '/feedback/statistics/trend',
    method: 'get',
    params: params
  })
}

// 获取反馈分类分布数据
export function getCategoryDistribution(params) {
  return request({
    url: '/feedback/statistics/categoryDistribution',
    method: 'get',
    params: params
  })
}

// 获取反馈状态分布数据
export function getStatusDistribution(params) {
  return request({
    url: '/feedback/statistics/statusDistribution',
    method: 'get',
    params: params
  })
}

// 获取反馈处理效率数据
export function getProcessingEfficiency(params) {
  return request({
    url: '/feedback/statistics/efficiency',
    method: 'get',
    params: params
  })
}

// 获取反馈满意度数据
export function getSatisfactionData(params) {
  return request({
    url: '/feedback/statistics/satisfaction',
    method: 'get',
    params: params
  })
}

// 查询反馈统计详细
export function getStatistics(id) {
  return request({
    url: '/feedback/statistics/' + id,
    method: 'get'
  })
}

// 新增反馈统计
export function addStatistics(data) {
  return request({
    url: '/feedback/statistics',
    method: 'post',
    data: data
  })
}

// 修改反馈统计
export function updateStatistics(data) {
  return request({
    url: '/feedback/statistics',
    method: 'put',
    data: data
  })
}

// 删除反馈统计
export function delStatistics(id) {
  return request({
    url: '/feedback/statistics/' + id,
    method: 'delete'
  })
}

// 生成日统计数据
export function generateDailyStatistics(statDate) {
  return request({
    url: '/feedback/statistics/generateDaily',
    method: 'post',
    params: {
      statDate: statDate
    }
  })
}

// 生成周统计数据
export function generateWeeklyStatistics(statDate) {
  return request({
    url: '/feedback/statistics/generateWeekly',
    method: 'post',
    params: {
      statDate: statDate
    }
  })
}

// 生成月统计数据
export function generateMonthlyStatistics(statDate) {
  return request({
    url: '/feedback/statistics/generateMonthly',
    method: 'post',
    params: {
      statDate: statDate
    }
  })
}

// 自动生成统计数据
export function autoGenerateStatistics() {
  return request({
    url: '/feedback/statistics/autoGenerate',
    method: 'post'
  })
}

// 清理过期统计数据
export function cleanExpiredStatistics(beforeDate) {
  return request({
    url: '/feedback/statistics/clean',
    method: 'delete',
    params: {
      beforeDate: beforeDate
    }
  })
}
