package com.ruoyi.web.controller.feedback;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.feedback.domain.UserFeedback;
import com.ruoyi.feedback.service.IUserFeedbackService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 用户反馈Controller
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@RestController
@RequestMapping("/feedback/user")
public class UserFeedbackController extends BaseController
{
    @Autowired
    private IUserFeedbackService userFeedbackService;

    /**
     * 查询用户反馈列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserFeedback userFeedback)
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackList(userFeedback);
        return getDataTable(list);
    }

    /**
     * 查询我的反馈列表
     */
    @GetMapping("/my")
    public TableDataInfo myFeedbackList(UserFeedback userFeedback)
    {
        startPage();
        userFeedback.setUserId(getUserId());
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackList(userFeedback);
        return getDataTable(list);
    }

    /**
     * 查询待处理的反馈列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:list')")
    @GetMapping("/pending")
    public TableDataInfo pendingList()
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectPendingUserFeedbackList();
        return getDataTable(list);
    }

    /**
     * 查询我处理的反馈列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:handle')")
    @GetMapping("/handled")
    public TableDataInfo handledList(UserFeedback userFeedback)
    {
        startPage();
        userFeedback.setHandlerId(getUserId());
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackList(userFeedback);
        return getDataTable(list);
    }

    /**
     * 根据知识库ID查询反馈列表
     */
    @GetMapping("/knowledge/{knowledgeBaseId}")
    public TableDataInfo getByKnowledgeBase(@PathVariable("knowledgeBaseId") Long knowledgeBaseId)
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackByKnowledgeBaseId(knowledgeBaseId);
        return getDataTable(list);
    }

    /**
     * 根据文档ID查询反馈列表
     */
    @GetMapping("/document/{documentId}")
    public TableDataInfo getByDocument(@PathVariable("documentId") Long documentId)
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackByDocumentId(documentId);
        return getDataTable(list);
    }

    /**
     * 根据AI会话ID查询反馈列表
     */
    @GetMapping("/session/{aiSessionId}")
    public TableDataInfo getByAiSession(@PathVariable("aiSessionId") String aiSessionId)
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackByAiSessionId(aiSessionId);
        return getDataTable(list);
    }

    /**
     * 获取反馈统计数据
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<Map<String, Object>> categoryStats = userFeedbackService.selectFeedbackCountByCategory();
        List<Map<String, Object>> statusStats = userFeedbackService.selectFeedbackCountByStatus();
        
        return success()
            .put("categoryStats", categoryStats)
            .put("statusStats", statusStats);
    }

    /**
     * 导出用户反馈列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:export')")
    @Log(title = "用户反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserFeedback userFeedback)
    {
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackList(userFeedback);
        ExcelUtil<UserFeedback> util = new ExcelUtil<UserFeedback>(UserFeedback.class);
        util.exportExcel(response, list, "用户反馈数据");
    }

    /**
     * 获取用户反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userFeedbackService.selectUserFeedbackById(id));
    }

    /**
     * 新增用户反馈
     */
    @Log(title = "用户反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserFeedback userFeedback)
    {
        userFeedback.setUserId(getUserId());
        userFeedback.setUserName(getUsername());
        userFeedback.setCreateBy(getUsername());
        return toAjax(userFeedbackService.submitUserFeedback(userFeedback));
    }

    /**
     * 修改用户反馈
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:edit')")
    @Log(title = "用户反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserFeedback userFeedback)
    {
        userFeedback.setUpdateBy(getUsername());
        return toAjax(userFeedbackService.updateUserFeedback(userFeedback));
    }

    /**
     * 分配反馈给处理人
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:assign')")
    @Log(title = "分配反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/assign/{id}")
    public AjaxResult assign(@PathVariable("id") Long id, 
                           @RequestParam("handlerId") Long handlerId,
                           @RequestParam("handlerName") String handlerName)
    {
        return toAjax(userFeedbackService.assignFeedback(id, handlerId, handlerName));
    }

    /**
     * 批量分配反馈给处理人
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:assign')")
    @Log(title = "批量分配反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/batchAssign")
    public AjaxResult batchAssign(@RequestParam("ids") Long[] ids,
                                @RequestParam("handlerId") Long handlerId,
                                @RequestParam("handlerName") String handlerName)
    {
        return toAjax(userFeedbackService.batchAssignFeedback(ids, handlerId, handlerName));
    }

    /**
     * 处理反馈
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:handle')")
    @Log(title = "处理反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/process/{id}")
    public AjaxResult process(@PathVariable("id") Long id, 
                            @RequestParam("handleResult") String handleResult)
    {
        return toAjax(userFeedbackService.processFeedback(id, handleResult));
    }

    /**
     * 关闭反馈
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:handle')")
    @Log(title = "关闭反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{id}")
    public AjaxResult close(@PathVariable("id") Long id, 
                          @RequestParam("closeReason") String closeReason)
    {
        return toAjax(userFeedbackService.closeFeedback(id, closeReason));
    }

    /**
     * 重新打开反馈
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:handle')")
    @Log(title = "重开反馈", businessType = BusinessType.UPDATE)
    @PutMapping("/reopen/{id}")
    public AjaxResult reopen(@PathVariable("id") Long id, 
                           @RequestParam("reopenReason") String reopenReason)
    {
        return toAjax(userFeedbackService.reopenFeedback(id, reopenReason));
    }

    /**
     * 批量更新反馈状态
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:edit')")
    @Log(title = "批量更新反馈状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestParam("ids") Long[] ids,
                                      @RequestParam("status") String status)
    {
        return toAjax(userFeedbackService.batchUpdateFeedbackStatus(ids, status));
    }

    /**
     * 删除用户反馈
     */
    @PreAuthorize("@ss.hasPermi('feedback:user:remove')")
    @Log(title = "用户反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userFeedbackService.deleteUserFeedbackByIds(ids));
    }

    /**
     * 自动收集反馈（系统调用）
     */
    @PostMapping("/autoCollect")
    public AjaxResult autoCollect(@RequestBody UserFeedback userFeedback)
    {
        return toAjax(userFeedbackService.autoCollectFeedback(userFeedback));
    }
}
