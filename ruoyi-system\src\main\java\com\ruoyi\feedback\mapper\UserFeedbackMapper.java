package com.ruoyi.feedback.mapper;

import java.util.List;
import com.ruoyi.feedback.domain.UserFeedback;

/**
 * 用户反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface UserFeedbackMapper 
{
    /**
     * 查询用户反馈
     * 
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public UserFeedback selectUserFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     * 
     * @param userFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackList(UserFeedback userFeedback);

    /**
     * 根据用户ID查询反馈列表
     * 
     * @param userId 用户ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByUserId(Long userId);

    /**
     * 根据知识库ID查询反馈列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据文档ID查询反馈列表
     * 
     * @param documentId 文档ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByDocumentId(Long documentId);

    /**
     * 根据AI会话ID查询反馈列表
     * 
     * @param aiSessionId AI会话ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByAiSessionId(String aiSessionId);

    /**
     * 根据分类ID查询反馈列表
     * 
     * @param categoryId 分类ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByCategoryId(Long categoryId);

    /**
     * 根据状态查询反馈列表
     * 
     * @param status 状态
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByStatus(String status);

    /**
     * 根据处理人ID查询反馈列表
     * 
     * @param handlerId 处理人ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByHandlerId(Long handlerId);

    /**
     * 查询待处理的反馈列表
     * 
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectPendingUserFeedbackList();

    /**
     * 统计反馈数量按分类
     * 
     * @return 统计结果
     */
    public List<UserFeedback> selectFeedbackCountByCategory();

    /**
     * 统计反馈数量按状态
     * 
     * @return 统计结果
     */
    public List<UserFeedback> selectFeedbackCountByStatus();

    /**
     * 新增用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int insertUserFeedback(UserFeedback userFeedback);

    /**
     * 修改用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int updateUserFeedback(UserFeedback userFeedback);

    /**
     * 批量更新反馈状态
     * 
     * @param ids 反馈ID数组
     * @param status 新状态
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @return 结果
     */
    public int batchUpdateFeedbackStatus(Long[] ids, String status, Long handlerId, String handlerName);

    /**
     * 删除用户反馈
     * 
     * @param id 用户反馈主键
     * @return 结果
     */
    public int deleteUserFeedbackById(Long id);

    /**
     * 批量删除用户反馈
     * 
     * @param ids 需要删除的用户反馈主键集合
     * @return 结果
     */
    public int deleteUserFeedbackByIds(Long[] ids);
}
