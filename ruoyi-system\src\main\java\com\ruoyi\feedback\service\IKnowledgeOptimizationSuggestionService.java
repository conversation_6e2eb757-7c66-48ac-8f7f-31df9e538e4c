package com.ruoyi.feedback.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.feedback.domain.KnowledgeOptimizationSuggestion;

/**
 * 知识库优化建议Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IKnowledgeOptimizationSuggestionService 
{
    /**
     * 查询知识库优化建议
     * 
     * @param id 知识库优化建议主键
     * @return 知识库优化建议
     */
    public KnowledgeOptimizationSuggestion selectKnowledgeOptimizationSuggestionById(Long id);

    /**
     * 查询知识库优化建议列表
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectKnowledgeOptimizationSuggestionList(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 根据建议类型查询优化建议列表
     * 
     * @param suggestionType 建议类型
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionBySuggestionType(String suggestionType);

    /**
     * 根据目标类型和目标ID查询优化建议列表
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectSuggestionByTarget(String targetType, Long targetId);

    /**
     * 查询自动生成的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectAutoGeneratedSuggestionList();

    /**
     * 查询待审核的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectPendingReviewSuggestionList();

    /**
     * 查询已通过待实施的优化建议列表
     * 
     * @return 知识库优化建议集合
     */
    public List<KnowledgeOptimizationSuggestion> selectApprovedSuggestionList();

    /**
     * 统计建议数量按类型
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectSuggestionCountByType();

    /**
     * 统计建议数量按状态
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectSuggestionCountByStatus();

    /**
     * 新增知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    public int insertKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 修改知识库优化建议
     * 
     * @param knowledgeOptimizationSuggestion 知识库优化建议
     * @return 结果
     */
    public int updateKnowledgeOptimizationSuggestion(KnowledgeOptimizationSuggestion knowledgeOptimizationSuggestion);

    /**
     * 审核优化建议
     * 
     * @param suggestionId 建议ID
     * @param status 审核状态（1通过 2拒绝）
     * @param reviewComment 审核意见
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 结果
     */
    public int reviewSuggestion(Long suggestionId, String status, String reviewComment, Long reviewerId, String reviewerName);

    /**
     * 批量审核优化建议
     * 
     * @param suggestionIds 建议ID数组
     * @param status 审核状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 结果
     */
    public int batchReviewSuggestion(Long[] suggestionIds, String status, Long reviewerId, String reviewerName);

    /**
     * 实施优化建议
     * 
     * @param suggestionId 建议ID
     * @param implementResult 实施结果
     * @param implementerId 实施人ID
     * @param implementerName 实施人姓名
     * @return 结果
     */
    public int implementSuggestion(Long suggestionId, String implementResult, Long implementerId, String implementerName);

    /**
     * 关闭优化建议
     * 
     * @param suggestionId 建议ID
     * @param closeReason 关闭原因
     * @return 结果
     */
    public int closeSuggestion(Long suggestionId, String closeReason);

    /**
     * 批量删除知识库优化建议
     * 
     * @param ids 需要删除的知识库优化建议主键集合
     * @return 结果
     */
    public int deleteKnowledgeOptimizationSuggestionByIds(Long[] ids);

    /**
     * 删除知识库优化建议信息
     * 
     * @param id 知识库优化建议主键
     * @return 结果
     */
    public int deleteKnowledgeOptimizationSuggestionById(Long id);

    /**
     * 基于反馈数据生成优化建议
     * 
     * @param feedbackIds 反馈ID列表
     * @return 结果
     */
    public int generateSuggestionFromFeedback(List<Long> feedbackIds);

    /**
     * 自动生成知识库优化建议
     * 
     * @param knowledgeBaseId 知识库ID（可为空，表示全部知识库）
     * @return 结果
     */
    public int autoGenerateOptimizationSuggestions(Long knowledgeBaseId);

    /**
     * 评估优化建议的效果
     * 
     * @param suggestionId 建议ID
     * @param effectEvaluation 效果评估
     * @return 结果
     */
    public int evaluateSuggestionEffect(Long suggestionId, String effectEvaluation);
}
