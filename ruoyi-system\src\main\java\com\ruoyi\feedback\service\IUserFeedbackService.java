package com.ruoyi.feedback.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.feedback.domain.UserFeedback;

/**
 * 用户反馈Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IUserFeedbackService 
{
    /**
     * 查询用户反馈
     * 
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public UserFeedback selectUserFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     * 
     * @param userFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackList(UserFeedback userFeedback);

    /**
     * 根据用户ID查询反馈列表
     * 
     * @param userId 用户ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByUserId(Long userId);

    /**
     * 根据知识库ID查询反馈列表
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByKnowledgeBaseId(Long knowledgeBaseId);

    /**
     * 根据文档ID查询反馈列表
     * 
     * @param documentId 文档ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByDocumentId(Long documentId);

    /**
     * 根据AI会话ID查询反馈列表
     * 
     * @param aiSessionId AI会话ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackByAiSessionId(String aiSessionId);

    /**
     * 查询待处理的反馈列表
     * 
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectPendingUserFeedbackList();

    /**
     * 统计反馈数量按分类
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectFeedbackCountByCategory();

    /**
     * 统计反馈数量按状态
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectFeedbackCountByStatus();

    /**
     * 新增用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int insertUserFeedback(UserFeedback userFeedback);

    /**
     * 修改用户反馈
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int updateUserFeedback(UserFeedback userFeedback);

    /**
     * 分配反馈给处理人
     * 
     * @param feedbackId 反馈ID
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @return 结果
     */
    public int assignFeedback(Long feedbackId, Long handlerId, String handlerName);

    /**
     * 批量分配反馈给处理人
     * 
     * @param feedbackIds 反馈ID数组
     * @param handlerId 处理人ID
     * @param handlerName 处理人姓名
     * @return 结果
     */
    public int batchAssignFeedback(Long[] feedbackIds, Long handlerId, String handlerName);

    /**
     * 处理反馈
     * 
     * @param feedbackId 反馈ID
     * @param handleResult 处理结果
     * @return 结果
     */
    public int processFeedback(Long feedbackId, String handleResult);

    /**
     * 关闭反馈
     * 
     * @param feedbackId 反馈ID
     * @param closeReason 关闭原因
     * @return 结果
     */
    public int closeFeedback(Long feedbackId, String closeReason);

    /**
     * 重新打开反馈
     * 
     * @param feedbackId 反馈ID
     * @param reopenReason 重开原因
     * @return 结果
     */
    public int reopenFeedback(Long feedbackId, String reopenReason);

    /**
     * 批量更新反馈状态
     * 
     * @param feedbackIds 反馈ID数组
     * @param status 新状态
     * @return 结果
     */
    public int batchUpdateFeedbackStatus(Long[] feedbackIds, String status);

    /**
     * 批量删除用户反馈
     * 
     * @param ids 需要删除的用户反馈主键集合
     * @return 结果
     */
    public int deleteUserFeedbackByIds(Long[] ids);

    /**
     * 删除用户反馈信息
     * 
     * @param id 用户反馈主键
     * @return 结果
     */
    public int deleteUserFeedbackById(Long id);

    /**
     * 提交用户反馈（包含业务逻辑处理）
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int submitUserFeedback(UserFeedback userFeedback);

    /**
     * 自动收集反馈（系统自动生成）
     * 
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int autoCollectFeedback(UserFeedback userFeedback);
}
