-- 反馈系统字典数据配置
-- 创建时间：2025-08-29

-- 获取下一个字典类型ID
SET @dict_id = (SELECT IFNULL(MAX(dict_id), 0) + 1 FROM sys_dict_type);

-- 1. 反馈状态字典
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '反馈状态', 'feedback_status', '0', 'admin', sysdate(), '', NULL, '反馈处理状态列表');

-- 反馈状态字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '待处理', '0', 'feedback_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', NULL, '待处理状态'),
(NULL, 2, '处理中', '1', 'feedback_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, '处理中状态'),
(NULL, 3, '已处理', '2', 'feedback_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已处理状态'),
(NULL, 4, '已关闭', '3', 'feedback_status', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '已关闭状态');

-- 2. 反馈来源类型字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '反馈来源类型', 'feedback_source_type', '0', 'admin', sysdate(), '', NULL, '反馈来源类型列表');

-- 反馈来源类型字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '用户手动提交', 'manual', 'feedback_source_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '用户手动提交反馈'),
(NULL, 2, '系统自动收集', 'auto', 'feedback_source_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '系统自动收集反馈'),
(NULL, 3, '管理员创建', 'system', 'feedback_source_type', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '管理员创建反馈');

-- 3. 建议类型字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '建议类型', 'suggestion_type', '0', 'admin', sysdate(), '', NULL, '优化建议类型列表');

-- 建议类型字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '内容优化', 'content', 'suggestion_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '内容优化建议'),
(NULL, 2, '结构调整', 'structure', 'suggestion_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '结构调整建议'),
(NULL, 3, '标签完善', 'tag', 'suggestion_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '标签完善建议'),
(NULL, 4, '检索优化', 'search', 'suggestion_type', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '检索优化建议');

-- 4. 建议状态字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '建议状态', 'suggestion_status', '0', 'admin', sysdate(), '', NULL, '优化建议状态列表');

-- 建议状态字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '待审核', '0', 'suggestion_status', '', 'warning', 'Y', '0', 'admin', sysdate(), '', NULL, '待审核状态'),
(NULL, 2, '已通过', '1', 'suggestion_status', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '已通过状态'),
(NULL, 3, '已拒绝', '2', 'suggestion_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '已拒绝状态'),
(NULL, 4, '已实施', '3', 'suggestion_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, '已实施状态'),
(NULL, 5, '已关闭', '4', 'suggestion_status', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '已关闭状态');

-- 5. 目标类型字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '目标类型', 'target_type', '0', 'admin', sysdate(), '', NULL, '优化建议目标类型列表');

-- 目标类型字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '知识库', 'knowledge_base', 'target_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '知识库目标'),
(NULL, 2, '文档', 'document', 'target_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '文档目标'),
(NULL, 3, '分类', 'category', 'target_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '分类目标');

-- 6. 优先级字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '优先级', 'priority_level', '0', 'admin', sysdate(), '', NULL, '优先级列表');

-- 优先级字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '高', '1', 'priority_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '高优先级'),
(NULL, 2, '中', '2', 'priority_level', '', 'warning', 'Y', '0', 'admin', sysdate(), '', NULL, '中优先级'),
(NULL, 3, '低', '3', 'priority_level', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '低优先级');

-- 7. 统计类型字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '统计类型', 'statistics_type', '0', 'admin', sysdate(), '', NULL, '统计类型列表');

-- 统计类型字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '日统计', 'daily', 'statistics_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '日统计'),
(NULL, 2, '周统计', 'weekly', 'statistics_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '周统计'),
(NULL, 3, '月统计', 'monthly', 'statistics_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '月统计');

-- 8. 操作类型字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '操作类型', 'action_type', '0', 'admin', sysdate(), '', NULL, '反馈处理操作类型列表');

-- 操作类型字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '创建', 'create', 'action_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '创建反馈'),
(NULL, 2, '分配', 'assign', 'action_type', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, '分配反馈'),
(NULL, 3, '处理', 'process', 'action_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '处理反馈'),
(NULL, 4, '关闭', 'close', 'action_type', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, '关闭反馈'),
(NULL, 5, '重开', 'reopen', 'action_type', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '重开反馈'),
(NULL, 6, '状态变更', 'status_change', 'action_type', '', 'default', 'N', '0', 'admin', sysdate(), '', NULL, '状态变更');

-- 9. 评分等级字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '评分等级', 'rating_level', '0', 'admin', sysdate(), '', NULL, '反馈评分等级列表');

-- 评分等级字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, '1分-很不满意', '1', 'rating_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, '1分评价'),
(NULL, 2, '2分-不满意', '2', 'rating_level', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '2分评价'),
(NULL, 3, '3分-一般', '3', 'rating_level', '', 'info', 'Y', '0', 'admin', sysdate(), '', NULL, '3分评价'),
(NULL, 4, '4分-满意', '4', 'rating_level', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, '4分评价'),
(NULL, 5, '5分-很满意', '5', 'rating_level', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '5分评价');

-- 10. 算法版本字典
SET @dict_id = @dict_id + 1;
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES (@dict_id, '算法版本', 'algorithm_version', '0', 'admin', sysdate(), '', NULL, '优化建议算法版本列表');

-- 算法版本字典数据
INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES 
(NULL, 1, 'v1.0', '1.0', 'algorithm_version', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, '算法版本1.0'),
(NULL, 2, 'v1.1', '1.1', 'algorithm_version', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, '算法版本1.1'),
(NULL, 3, 'v2.0', '2.0', 'algorithm_version', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, '算法版本2.0');
