package com.ruoyi.feedback.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户反馈对象 user_feedback
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class UserFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 反馈ID */
    private Long id;

    /** 反馈标题 */
    @Excel(name = "反馈标题")
    private String title;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 反馈分类ID */
    @Excel(name = "反馈分类ID")
    private Long categoryId;

    /** 分类名称（冗余字段） */
    @Excel(name = "分类名称")
    private String categoryName;

    /** 评分（1-5分） */
    @Excel(name = "评分")
    private Integer rating;

    /** 反馈用户ID */
    @Excel(name = "反馈用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 关联知识库ID */
    @Excel(name = "关联知识库ID")
    private Long knowledgeBaseId;

    /** 知识库名称（冗余字段） */
    @Excel(name = "知识库名称")
    private String knowledgeBaseName;

    /** 关联文档ID */
    @Excel(name = "关联文档ID")
    private Long documentId;

    /** 文档名称（冗余字段） */
    @Excel(name = "文档名称")
    private String documentName;

    /** 关联AI会话ID */
    @Excel(name = "关联AI会话ID")
    private String aiSessionId;

    /** 反馈来源（manual用户手动提交 auto系统自动收集 system管理员创建） */
    @Excel(name = "反馈来源", readConverterExp = "manual=用户手动提交,auto=系统自动收集,system=管理员创建")
    private String sourceType;

    /** 反馈来源页面 */
    @Excel(name = "反馈来源页面")
    private String sourcePage;

    /** 处理状态（0待处理 1处理中 2已处理 3已关闭） */
    @Excel(name = "处理状态", readConverterExp = "0=待处理,1=处理中,2=已处理,3=已关闭")
    private String status;

    /** 处理人ID */
    @Excel(name = "处理人ID")
    private Long handlerId;

    /** 处理人姓名 */
    @Excel(name = "处理人姓名")
    private String handlerName;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String handleResult;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setCategoryName(String categoryName) 
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName() 
    {
        return categoryName;
    }

    public void setRating(Integer rating) 
    {
        this.rating = rating;
    }

    public Integer getRating() 
    {
        return rating;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) 
    {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public Long getKnowledgeBaseId() 
    {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseName(String knowledgeBaseName) 
    {
        this.knowledgeBaseName = knowledgeBaseName;
    }

    public String getKnowledgeBaseName() 
    {
        return knowledgeBaseName;
    }

    public void setDocumentId(Long documentId) 
    {
        this.documentId = documentId;
    }

    public Long getDocumentId() 
    {
        return documentId;
    }

    public void setDocumentName(String documentName) 
    {
        this.documentName = documentName;
    }

    public String getDocumentName() 
    {
        return documentName;
    }

    public void setAiSessionId(String aiSessionId) 
    {
        this.aiSessionId = aiSessionId;
    }

    public String getAiSessionId() 
    {
        return aiSessionId;
    }

    public void setSourceType(String sourceType) 
    {
        this.sourceType = sourceType;
    }

    public String getSourceType() 
    {
        return sourceType;
    }

    public void setSourcePage(String sourcePage) 
    {
        this.sourcePage = sourcePage;
    }

    public String getSourcePage() 
    {
        return sourcePage;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setHandlerId(Long handlerId) 
    {
        this.handlerId = handlerId;
    }

    public Long getHandlerId() 
    {
        return handlerId;
    }

    public void setHandlerName(String handlerName) 
    {
        this.handlerName = handlerName;
    }

    public String getHandlerName() 
    {
        return handlerName;
    }

    public void setHandleTime(Date handleTime) 
    {
        this.handleTime = handleTime;
    }

    public Date getHandleTime() 
    {
        return handleTime;
    }

    public void setHandleResult(String handleResult) 
    {
        this.handleResult = handleResult;
    }

    public String getHandleResult() 
    {
        return handleResult;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("categoryId", getCategoryId())
            .append("categoryName", getCategoryName())
            .append("rating", getRating())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("knowledgeBaseId", getKnowledgeBaseId())
            .append("knowledgeBaseName", getKnowledgeBaseName())
            .append("documentId", getDocumentId())
            .append("documentName", getDocumentName())
            .append("aiSessionId", getAiSessionId())
            .append("sourceType", getSourceType())
            .append("sourcePage", getSourcePage())
            .append("status", getStatus())
            .append("handlerId", getHandlerId())
            .append("handlerName", getHandlerName())
            .append("handleTime", getHandleTime())
            .append("handleResult", getHandleResult())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
