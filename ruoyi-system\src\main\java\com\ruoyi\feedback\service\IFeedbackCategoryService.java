package com.ruoyi.feedback.service;

import java.util.List;
import com.ruoyi.feedback.domain.FeedbackCategory;

/**
 * 反馈分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface IFeedbackCategoryService 
{
    /**
     * 查询反馈分类
     * 
     * @param id 反馈分类主键
     * @return 反馈分类
     */
    public FeedbackCategory selectFeedbackCategoryById(Long id);

    /**
     * 根据分类编码查询反馈分类
     * 
     * @param code 分类编码
     * @return 反馈分类
     */
    public FeedbackCategory selectFeedbackCategoryByCode(String code);

    /**
     * 查询反馈分类列表
     * 
     * @param feedbackCategory 反馈分类
     * @return 反馈分类集合
     */
    public List<FeedbackCategory> selectFeedbackCategoryList(FeedbackCategory feedbackCategory);

    /**
     * 查询启用的反馈分类列表
     * 
     * @return 反馈分类集合
     */
    public List<FeedbackCategory> selectEnabledFeedbackCategoryList();

    /**
     * 新增反馈分类
     * 
     * @param feedbackCategory 反馈分类
     * @return 结果
     */
    public int insertFeedbackCategory(FeedbackCategory feedbackCategory);

    /**
     * 修改反馈分类
     * 
     * @param feedbackCategory 反馈分类
     * @return 结果
     */
    public int updateFeedbackCategory(FeedbackCategory feedbackCategory);

    /**
     * 批量删除反馈分类
     * 
     * @param ids 需要删除的反馈分类主键集合
     * @return 结果
     */
    public int deleteFeedbackCategoryByIds(Long[] ids);

    /**
     * 删除反馈分类信息
     * 
     * @param id 反馈分类主键
     * @return 结果
     */
    public int deleteFeedbackCategoryById(Long id);

    /**
     * 检查分类编码是否唯一
     * 
     * @param feedbackCategory 反馈分类信息
     * @return 结果
     */
    public boolean checkCodeUnique(FeedbackCategory feedbackCategory);
}
