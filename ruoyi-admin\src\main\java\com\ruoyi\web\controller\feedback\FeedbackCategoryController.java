package com.ruoyi.web.controller.feedback;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.feedback.domain.FeedbackCategory;
import com.ruoyi.feedback.service.IFeedbackCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 反馈分类Controller
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@RestController
@RequestMapping("/feedback/category")
public class FeedbackCategoryController extends BaseController
{
    @Autowired
    private IFeedbackCategoryService feedbackCategoryService;

    /**
     * 查询反馈分类列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(FeedbackCategory feedbackCategory)
    {
        startPage();
        List<FeedbackCategory> list = feedbackCategoryService.selectFeedbackCategoryList(feedbackCategory);
        return getDataTable(list);
    }

    /**
     * 查询启用的反馈分类列表（用于下拉选择）
     */
    @GetMapping("/enabled")
    public AjaxResult getEnabledCategories()
    {
        List<FeedbackCategory> list = feedbackCategoryService.selectEnabledFeedbackCategoryList();
        return success(list);
    }

    /**
     * 导出反馈分类列表
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:export')")
    @Log(title = "反馈分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FeedbackCategory feedbackCategory)
    {
        List<FeedbackCategory> list = feedbackCategoryService.selectFeedbackCategoryList(feedbackCategory);
        ExcelUtil<FeedbackCategory> util = new ExcelUtil<FeedbackCategory>(FeedbackCategory.class);
        util.exportExcel(response, list, "反馈分类数据");
    }

    /**
     * 获取反馈分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(feedbackCategoryService.selectFeedbackCategoryById(id));
    }

    /**
     * 根据分类编码获取反馈分类信息
     */
    @GetMapping(value = "/code/{code}")
    public AjaxResult getInfoByCode(@PathVariable("code") String code)
    {
        return success(feedbackCategoryService.selectFeedbackCategoryByCode(code));
    }

    /**
     * 新增反馈分类
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:add')")
    @Log(title = "反馈分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FeedbackCategory feedbackCategory)
    {
        if (!feedbackCategoryService.checkCodeUnique(feedbackCategory))
        {
            return error("新增反馈分类'" + feedbackCategory.getName() + "'失败，分类编码已存在");
        }
        feedbackCategory.setCreateBy(getUsername());
        return toAjax(feedbackCategoryService.insertFeedbackCategory(feedbackCategory));
    }

    /**
     * 修改反馈分类
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:edit')")
    @Log(title = "反馈分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FeedbackCategory feedbackCategory)
    {
        if (!feedbackCategoryService.checkCodeUnique(feedbackCategory))
        {
            return error("修改反馈分类'" + feedbackCategory.getName() + "'失败，分类编码已存在");
        }
        feedbackCategory.setUpdateBy(getUsername());
        return toAjax(feedbackCategoryService.updateFeedbackCategory(feedbackCategory));
    }

    /**
     * 删除反馈分类
     */
    @PreAuthorize("@ss.hasPermi('feedback:category:remove')")
    @Log(title = "反馈分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(feedbackCategoryService.deleteFeedbackCategoryByIds(ids));
    }

    /**
     * 检查分类编码是否唯一
     */
    @PostMapping("/checkCodeUnique")
    public AjaxResult checkCodeUnique(@RequestBody FeedbackCategory feedbackCategory)
    {
        return success(feedbackCategoryService.checkCodeUnique(feedbackCategory));
    }
}
